<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Événements - Notiflair Admin</title>
    
    <!-- Google Fonts - Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com/3.3.0"></script>
    
    <style>
        * {
            font-family: 'Inter', sans-serif;
        }
        
        /* Scrollbar personnalisée */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
        
        /* Menu item actif */
        .menu-active {
            background-color: rgba(59, 130, 246, 0.1);
            border-left: 3px solid #2563eb;
        }
        
        .menu-active span {
            color: #3b82f6;
            font-weight: 600;
        }
        
        .menu-active svg {
            color: #3b82f6;
        }
        
        /* Tree node styles */
        .tree-node {
            transition: all 0.2s ease;
        }
        
        .tree-node:hover {
            background-color: #f3f4f6;
        }
        
        .tree-node.active {
            background-color: #eff6ff;
            border-left: 3px solid #2563eb;
        }
        
        .tree-line {
            border-left: 1px solid #e5e7eb;
        }
        
        /* Slide panel animation */
        .slide-panel {
            transition: transform 0.3s ease-out;
        }
        
        .slide-panel.closed {
            transform: translateX(100%);
        }
        
        /* Status badges */
        .status-active {
            background-color: #d1fae5;
            color: #065f46;
        }
        
        .status-inactive {
            background-color: #fee2e2;
            color: #991b1b;
        }
        
        /* Country flags */
        .flag-fr { background-color: #3b82f6; }
        .flag-ch { background-color: #ef4444; }
        .flag-be { background-color: #f59e0b; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Container principal -->
    <div class="min-h-screen flex">
        
        <!-- Sidebar Admin -->
        <aside id="sidebar" class="w-64 bg-gray-900 text-white flex flex-col fixed h-full lg:relative lg:translate-x-0 -translate-x-full transition-transform duration-300 z-30">
            <!-- Logo -->
            <div class="p-6 border-b border-gray-800">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-blue-600 rounded-xl flex items-center justify-center shadow">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold">Notiflair</h1>
                        <p class="text-xs text-gray-400">Administration</p>
                    </div>
                </div>
            </div>
            
            <!-- Navigation -->
            <nav class="flex-1 p-4 space-y-1 overflow-y-auto">
                <a href="#" class="flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:bg-gray-800 transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <span>Vue d'ensemble</span>
                </a>
                
                <a href="#" class="menu-active flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:bg-gray-800 transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <span>Événements</span>
                    <span class="ml-auto bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">1,234</span>
                </a>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <div class="flex-1 flex flex-col">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-20">
                <div class="flex items-center justify-between px-6 py-4">
                    <button id="menuToggle" class="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                    
                    <h2 class="text-2xl font-bold text-gray-900">Gestion des Événements</h2>
                    
                    <div class="flex items-center space-x-4">
                        <button class="p-2 rounded-lg hover:bg-gray-100 transition-colors">
                            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </header>
            
            <!-- Content Area with Tree and Detail Panel -->
            <div class="flex-1 flex overflow-hidden">
                <!-- Tree View -->
                <div class="w-full lg:w-1/2 xl:w-2/5 bg-white border-r border-gray-200 overflow-y-auto">
                    <div class="p-6">
                        <!-- Search and filters (pour plus tard) -->
                        <div class="mb-6">
                            <div class="relative">
                                <input type="text" placeholder="Rechercher un événement..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <svg class="w-5 h-5 text-gray-400 absolute left-3 top-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>
                        
                        <!-- Tree Structure -->
                        <div class="space-y-1">
                            <!-- Type: Santé & Bien-être -->
                            <div>
                                <div class="tree-node flex items-center p-3 rounded-lg cursor-pointer" onclick="showDetails('type', 1)">
                                    <svg class="w-4 h-4 text-gray-400 mr-2 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" onclick="event.stopPropagation(); toggleNode(this.parentElement)">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex items-center">
                                            <span class="font-medium text-gray-900">Santé & Bien-être</span>
                                            <span class="ml-2 text-xs text-gray-500">(12 événements)</span>
                                        </div>
                                        <div class="flex items-center mt-1 space-x-2">
                                            <span class="text-xs status-active px-2 py-0.5 rounded">Actif</span>
                                            <div class="flex space-x-1">
                                                <span class="text-xs px-1.5 py-0.5 bg-blue-100 text-blue-700 rounded">FR</span>
                                                <span class="text-xs px-1.5 py-0.5 bg-red-100 text-red-700 rounded">CH</span>
                                                <span class="text-xs px-1.5 py-0.5 bg-yellow-100 text-yellow-700 rounded">BE</span>
                                            </div>
                                            <span class="text-xs text-gray-500">ID: health_wellness</span>
                                        </div>
                                    </div>
                                    <span class="text-xs text-gray-400">1,245 usages</span>
                                </div>
                                
                                <!-- Sub-items -->
                                <div class="ml-6 hidden">
                                    <!-- Event: Rendez-vous médecin -->
                                    <div class="tree-node flex items-center p-3 rounded-lg cursor-pointer border-l-2 border-gray-200 ml-3" onclick="showDetails('event', 101)">
                                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3 ml-4">
                                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <div class="flex items-center">
                                                <span class="font-medium text-gray-900">Rendez-vous médecin</span>
                                            </div>
                                            <div class="flex items-center mt-1 space-x-2">
                                                <span class="text-xs status-active px-2 py-0.5 rounded">Actif</span>
                                                <span class="text-xs px-1.5 py-0.5 bg-blue-100 text-blue-700 rounded">FR</span>
                                                <span class="text-xs text-gray-500">doctor_appointment</span>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <span class="text-xs text-gray-400">823 usages</span>
                                            <p class="text-xs text-gray-500">Score: 0.85</p>
                                        </div>
                                    </div>
                                    
                                    <!-- Event: Prise de médicament -->
                                    <div class="tree-node flex items-center p-3 rounded-lg cursor-pointer border-l-2 border-gray-200 ml-3" onclick="showDetails('event', 102)">
                                        <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3 ml-4">
                                            <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <div class="flex items-center">
                                                <span class="font-medium text-gray-900">Prise de médicament</span>
                                            </div>
                                            <div class="flex items-center mt-1 space-x-2">
                                                <span class="text-xs status-active px-2 py-0.5 rounded">Actif</span>
                                                <span class="text-xs px-1.5 py-0.5 bg-blue-100 text-blue-700 rounded">FR</span>
                                                <span class="text-xs px-1.5 py-0.5 bg-red-100 text-red-700 rounded">CH</span>
                                                <span class="text-xs text-gray-500">medication_reminder</span>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <span class="text-xs text-gray-400">422 usages</span>
                                            <p class="text-xs text-gray-500">Score: 0.72</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Type: Fêtes & Célébrations -->
                            <div>
                                <div class="tree-node flex items-center p-3 rounded-lg cursor-pointer" onclick="toggleNode(this); showDetails('type', 2)">
                                    <svg class="w-4 h-4 text-gray-400 mr-2 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 15.546c-.523 0-1.046.151-1.5.454a2.704 2.704 0 01-3 0 2.704 2.704 0 00-3 0 2.704 2.704 0 01-3 0 2.704 2.704 0 00-3 0 2.704 2.704 0 01-3 0 2.701 2.701 0 00-1.5-.454M9 6v2m3-2v2m3-2v2M9 3h.01M12 3h.01M15 3h.01M21 21v-7a2 2 0 00-2-2H5a2 2 0 00-2 2v7h18zm-3-9v-2a2 2 0 00-2-2H8a2 2 0 00-2 2v2h12z"></path>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex items-center">
                                            <span class="font-medium text-gray-900">Fêtes & Célébrations</span>
                                            <span class="ml-2 text-xs text-gray-500">(45 événements)</span>
                                        </div>
                                        <div class="flex items-center mt-1 space-x-2">
                                            <span class="text-xs status-active px-2 py-0.5 rounded">Actif</span>
                                            <div class="flex space-x-1">
                                                <span class="text-xs px-1.5 py-0.5 bg-blue-100 text-blue-700 rounded">FR</span>
                                                <span class="text-xs px-1.5 py-0.5 bg-red-100 text-red-700 rounded">CH</span>
                                                <span class="text-xs px-1.5 py-0.5 bg-yellow-100 text-yellow-700 rounded">BE</span>
                                            </div>
                                            <span class="text-xs text-gray-500">ID: celebrations</span>
                                        </div>
                                    </div>
                                    <span class="text-xs text-gray-400">5,432 usages</span>
                                </div>
                                
                                <!-- Sub-items -->
                                <div class="ml-6 hidden">
                                    <!-- Sub-type: Fêtes nationales -->
                                    <div>
                                        <div class="tree-node flex items-center p-3 rounded-lg cursor-pointer border-l-2 border-gray-200 ml-3" onclick="toggleNode(this); showDetails('type', 21)">
                                            <svg class="w-4 h-4 text-gray-400 mr-2 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                            </svg>
                                            <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                                                <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 1H21l-3 6 3 6h-8.5l-1-1H5a2 2 0 00-2 2zm9-13.5V9"></path>
                                                </svg>
                                            </div>
                                            <div class="flex-1">
                                                <div class="flex items-center">
                                                    <span class="font-medium text-gray-900">Fêtes nationales</span>
                                                    <span class="ml-2 text-xs text-gray-500">(8 événements)</span>
                                                </div>
                                                <div class="flex items-center mt-1 space-x-2">
                                                    <span class="text-xs status-active px-2 py-0.5 rounded">Actif</span>
                                                    <span class="text-xs text-gray-500">ID: national_holidays</span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Events sous Fêtes nationales -->
                                        <div class="ml-6 hidden">
                                            <div class="tree-node flex items-center p-3 rounded-lg cursor-pointer border-l-2 border-gray-200 ml-3" onclick="showDetails('event', 201)">
                                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3 ml-4">
                                                    <span class="text-lg">🇫🇷</span>
                                                </div>
                                                <div class="flex-1">
                                                    <div class="flex items-center">
                                                        <span class="font-medium text-gray-900">14 Juillet</span>
                                                    </div>
                                                    <div class="flex items-center mt-1 space-x-2">
                                                        <span class="text-xs status-active px-2 py-0.5 rounded">Actif</span>
                                                        <span class="text-xs px-1.5 py-0.5 bg-blue-100 text-blue-700 rounded">FR</span>
                                                        <span class="text-xs text-gray-500">bastille_day</span>
                                                    </div>
                                                </div>
                                                <div class="text-right">
                                                    <span class="text-xs text-gray-400">15,234 usages</span>
                                                    <p class="text-xs text-gray-500">Score: 0.95</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Type: Maison & Quotidien -->
                            <div>
                                <div class="tree-node flex items-center p-3 rounded-lg cursor-pointer" onclick="toggleNode(this); showDetails('type', 3)">
                                    <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex items-center">
                                            <span class="font-medium text-gray-900">Maison & Quotidien</span>
                                            <span class="ml-2 text-xs text-gray-500">(23 événements)</span>
                                        </div>
                                        <div class="flex items-center mt-1 space-x-2">
                                            <span class="text-xs status-active px-2 py-0.5 rounded">Actif</span>
                                            <div class="flex space-x-1">
                                                <span class="text-xs px-1.5 py-0.5 bg-blue-100 text-blue-700 rounded">FR</span>
                                                <span class="text-xs px-1.5 py-0.5 bg-red-100 text-red-700 rounded">CH</span>
                                            </div>
                                            <span class="text-xs text-gray-500">ID: home_daily</span>
                                        </div>
                                    </div>
                                    <span class="text-xs text-gray-400">3,221 usages</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Detail Panel -->
                <div id="detailPanel" class="slide-panel closed fixed right-0 top-0 h-full w-full lg:w-1/2 xl:w-3/5 bg-white shadow-2xl lg:shadow-none lg:relative lg:translate-x-0 z-40 overflow-y-auto">
                    <!-- Panel vide par défaut -->
                    <div id="emptyState" class="h-full flex items-center justify-center">
                        <div class="text-center">
                            <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                            </svg>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Sélectionnez un élément</h3>
                            <p class="text-gray-500">Cliquez sur un type d'événement ou un événement dans l'arbre pour voir ses détails</p>
                        </div>
                    </div>
                    
                    <!-- Panel content (hidden by default) -->
                    <div id="panelContent" class="hidden">
                        <!-- Header -->
                        <div class="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
                            <h3 id="panelTitle" class="text-xl font-semibold text-gray-900"></h3>
                            <button onclick="closePanel()" class="p-2 rounded-lg hover:bg-gray-100 transition-colors">
                                <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                        
                        <!-- Content -->
                        <div id="panelBody" class="p-6">
                            <!-- Le contenu sera injecté dynamiquement -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Overlay mobile -->
    <div id="overlay" class="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden hidden"></div>
    
    <script>
        // Toggle sidebar mobile
        const menuToggle = document.getElementById('menuToggle');
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');
        
        menuToggle.addEventListener('click', () => {
            sidebar.classList.toggle('-translate-x-full');
            overlay.classList.toggle('hidden');
        });
        
        overlay.addEventListener('click', () => {
            sidebar.classList.add('-translate-x-full');
            overlay.classList.add('hidden');
        });
        
        // Toggle tree nodes
        function toggleNode(element) {
            event.stopPropagation(); // Empêche la propagation vers showDetails
            const arrow = element.querySelector('svg');
            const children = element.parentElement.querySelector('.ml-6');
            
            if (children) {
                children.classList.toggle('hidden');
                arrow.classList.toggle('rotate-90');
            }
        }
        
        // Show details panel
        function showDetails(type, id) {
            // Remove active class from all nodes
            document.querySelectorAll('.tree-node').forEach(node => {
                node.classList.remove('active');
            });
            
            // Add active class to clicked node
            event.currentTarget.classList.add('active');
            
            // Show panel
            const panel = document.getElementById('detailPanel');
            const emptyState = document.getElementById('emptyState');
            const panelContent = document.getElementById('panelContent');
            const panelTitle = document.getElementById('panelTitle');
            const panelBody = document.getElementById('panelBody');
            
            panel.classList.remove('closed');
            emptyState.classList.add('hidden');
            panelContent.classList.remove('hidden');
            
            // Update content based on type
            if (type === 'type') {
                showTypeDetails(id, panelTitle, panelBody);
            } else {
                showEventDetails(id, panelTitle, panelBody);
            }
            
            // Show overlay on mobile
            if (window.innerWidth < 1024) {
                overlay.classList.remove('hidden');
            }
        }
        
        // Close panel
        function closePanel() {
            const panel = document.getElementById('detailPanel');
            panel.classList.add('closed');
            overlay.classList.add('hidden');
            
            // Remove active state
            document.querySelectorAll('.tree-node').forEach(node => {
                node.classList.remove('active');
            });
        }
        
        // Show type details
        function showTypeDetails(id, titleEl, bodyEl) {
            titleEl.textContent = 'Type: Santé & Bien-être';
            
            bodyEl.innerHTML = `
                <!-- Informations non-éditables en haut -->
                <div class="space-y-6 mb-8">
                    <!-- Informations générales -->
                    <div>
                        <h4 class="text-sm font-semibold text-gray-700 uppercase tracking-wider mb-4">Informations générales</h4>
                        <div class="bg-gray-50 rounded-lg p-4 space-y-3">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">ID système</span>
                                <span class="text-sm font-mono text-gray-900">health_wellness</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Niveau</span>
                                <span class="text-sm text-gray-900">0 (Racine)</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Créé le</span>
                                <span class="text-sm text-gray-900">15/03/2023 14:32</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Dernière modification</span>
                                <span class="text-sm text-gray-900">22/11/2023 09:15</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Statistiques -->
                    <div>
                        <h4 class="text-sm font-semibold text-gray-700 uppercase tracking-wider mb-4">Statistiques</h4>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="bg-blue-50 rounded-lg p-4">
                                <p class="text-2xl font-bold text-blue-900">1,245</p>
                                <p class="text-sm text-blue-700">Utilisations totales</p>
                            </div>
                            <div class="bg-green-50 rounded-lg p-4">
                                <p class="text-2xl font-bold text-green-900">12</p>
                                <p class="text-sm text-green-700">Événements enfants</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Formulaire éditable -->
                <form class="space-y-6">
                    <!-- État et configuration -->
                    <div>
                        <h4 class="text-sm font-semibold text-gray-700 uppercase tracking-wider mb-4">État et Configuration</h4>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <label class="text-sm font-medium text-gray-700">État</label>
                                    <p class="text-xs text-gray-500">Activer ou désactiver ce type d'événement</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer" checked>
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>
                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Activé par défaut</label>
                                    <p class="text-xs text-gray-500">Ce type sera activé pour les nouveaux utilisateurs</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer" checked>
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Ordre d'affichage</label>
                                <input type="number" value="1" class="w-32 px-3 py-2 border border-gray-300 rounded-lg">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Traductions groupées par pays -->
                    <div>
                        <h4 class="text-sm font-semibold text-gray-700 uppercase tracking-wider mb-4">Traductions par pays</h4>
                        <div class="border border-gray-200 rounded-lg overflow-hidden" style="max-height: 400px;">
                            <div class="overflow-y-auto" style="max-height: 400px;">
                                <div class="space-y-0">
                                    <!-- France -->
                                    <div class="border-b border-gray-200">
                                        <div class="bg-blue-50 px-4 py-2 flex items-center space-x-2 sticky top-0 z-10">
                                            <span class="text-lg">🇫🇷</span>
                                            <span class="font-medium text-gray-900">France</span>
                                        </div>
                                        <table class="w-full">
                                            <thead class="bg-gray-50 sticky top-10 z-10">
                                                <tr>
                                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Langue</th>
                                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Label</th>
                                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                                                </tr>
                                            </thead>
                                            <tbody class="divide-y divide-gray-200">
                                                <tr>
                                                    <td class="px-4 py-3 text-sm font-medium text-gray-900">Français</td>
                                                    <td class="px-4 py-3">
                                                        <input type="text" value="Santé & Bien-être" class="w-full px-2 py-1 border border-gray-300 rounded text-sm">
                                                    </td>
                                                    <td class="px-4 py-3">
                                                        <input type="text" value="Événements liés à la santé et au bien-être" class="w-full px-2 py-1 border border-gray-300 rounded text-sm">
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    
                                    <!-- Suisse -->
                                    <div class="border-b border-gray-200">
                                        <div class="bg-red-50 px-4 py-2 flex items-center space-x-2 sticky top-0 z-10">
                                            <span class="text-lg">🇨🇭</span>
                                            <span class="font-medium text-gray-900">Suisse</span>
                                        </div>
                                        <table class="w-full">
                                            <thead class="bg-gray-50 sticky top-10 z-10">
                                                <tr>
                                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Langue</th>
                                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Label</th>
                                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                                                </tr>
                                            </thead>
                                            <tbody class="divide-y divide-gray-200">
                                                <tr>
                                                    <td class="px-4 py-3 text-sm font-medium text-gray-900">Français</td>
                                                    <td class="px-4 py-3">
                                                        <input type="text" value="Santé & Bien-être" class="w-full px-2 py-1 border border-gray-300 rounded text-sm">
                                                    </td>
                                                    <td class="px-4 py-3">
                                                        <input type="text" value="Événements liés à la santé et au bien-être" class="w-full px-2 py-1 border border-gray-300 rounded text-sm">
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="px-4 py-3 text-sm font-medium text-gray-900">Allemand</td>
                                                    <td class="px-4 py-3">
                                                        <input type="text" value="Gesundheit & Wohlbefinden" class="w-full px-2 py-1 border border-gray-300 rounded text-sm">
                                                    </td>
                                                    <td class="px-4 py-3">
                                                        <input type="text" value="Gesundheits- und Wellness-Veranstaltungen" class="w-full px-2 py-1 border border-gray-300 rounded text-sm">
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="px-4 py-3 text-sm font-medium text-gray-900">Italien</td>
                                                    <td class="px-4 py-3">
                                                        <input type="text" value="Salute e Benessere" class="w-full px-2 py-1 border border-gray-300 rounded text-sm">
                                                    </td>
                                                    <td class="px-4 py-3">
                                                        <input type="text" value="Eventi legati alla salute e al benessere" class="w-full px-2 py-1 border border-gray-300 rounded text-sm">
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    
                                    <!-- Belgique -->
                                    <div class="border-b border-gray-200">
                                        <div class="bg-yellow-50 px-4 py-2 flex items-center space-x-2 sticky top-0 z-10">
                                            <span class="text-lg">🇧🇪</span>
                                            <span class="font-medium text-gray-900">Belgique</span>
                                        </div>
                                        <table class="w-full">
                                            <thead class="bg-gray-50 sticky top-10 z-10">
                                                <tr>
                                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Langue</th>
                                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Label</th>
                                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                                                </tr>
                                            </thead>
                                            <tbody class="divide-y divide-gray-200">
                                                <tr>
                                                    <td class="px-4 py-3 text-sm font-medium text-gray-900">Français</td>
                                                    <td class="px-4 py-3">
                                                        <input type="text" value="Santé & Bien-être" class="w-full px-2 py-1 border border-gray-300 rounded text-sm">
                                                    </td>
                                                    <td class="px-4 py-3">
                                                        <input type="text" value="Événements liés à la santé et au bien-être" class="w-full px-2 py-1 border border-gray-300 rounded text-sm">
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="px-4 py-3 text-sm font-medium text-gray-900">Néerlandais</td>
                                                    <td class="px-4 py-3">
                                                        <input type="text" value="Gezondheid & Welzijn" class="w-full px-2 py-1 border border-gray-300 rounded text-sm">
                                                    </td>
                                                    <td class="px-4 py-3">
                                                        <input type="text" value="Gezondheids- en welzijnsevenementen" class="w-full px-2 py-1 border border-gray-300 rounded text-sm">
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Apparence -->
                    <div>
                        <h4 class="text-sm font-semibold text-gray-700 uppercase tracking-wider mb-4">Apparence</h4>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Icône</label>
                                <div class="flex items-center space-x-2">
                                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                        </svg>
                                    </div>
                                    <input type="text" value="heart-outline" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg font-mono text-sm">
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Couleurs</label>
                                <div class="flex space-x-4">
                                    <div class="flex-1">
                                        <label class="text-xs text-gray-500">Couleur principale</label>
                                        <div class="flex items-center space-x-2 mt-1">
                                            <input 
                                                type="color" 
                                                value="#10b981" 
                                                class="w-10 h-10 rounded border border-gray-300 cursor-pointer"
                                                onchange="document.getElementById('colorHex').value = this.value"
                                            >
                                            <input 
                                                id="colorHex"
                                                type="text" 
                                                value="#10b981" 
                                                class="flex-1 px-3 py-2 border border-gray-300 rounded-lg font-mono text-sm"
                                                pattern="^#[0-9A-Fa-f]{6}$"
                                                onchange="document.querySelector('input[type=color]').value = this.value"
                                            >
                                        </div>
                                    </div>
                                    <div class="flex-1">
                                        <label class="text-xs text-gray-500">Couleur de fond</label>
                                        <div class="flex items-center space-x-2 mt-1">
                                            <input 
                                                type="color" 
                                                value="#d1fae5" 
                                                class="w-10 h-10 rounded border border-gray-300 cursor-pointer"
                                                onchange="document.getElementById('bgColorHex').value = this.value"
                                            >
                                            <input 
                                                id="bgColorHex"
                                                type="text" 
                                                value="#d1fae5" 
                                                class="flex-1 px-3 py-2 border border-gray-300 rounded-lg font-mono text-sm"
                                                pattern="^#[0-9A-Fa-f]{6}$"
                                                onchange="document.querySelectorAll('input[type=color]')[1].value = this.value"
                                            >
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            `;
        }
        
        // Show event details
        function showEventDetails(id, titleEl, bodyEl) {
            titleEl.textContent = 'Événement: Rendez-vous médecin';
            
            bodyEl.innerHTML = `
                <!-- Informations générales -->
                <div class="mb-8">
                    <h4 class="text-sm font-semibold text-gray-700 uppercase tracking-wider mb-4">Informations générales</h4>
                    <div class="bg-gray-50 rounded-lg p-4 space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">ID système</span>
                            <span class="text-sm font-mono text-gray-900">doctor_appointment</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Type parent</span>
                            <span class="text-sm text-gray-900">Santé & Bien-être</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Score de pertinence</span>
                            <span class="text-sm font-medium text-gray-900">0.85</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Utilisations</span>
                            <span class="text-sm text-gray-900">823</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Dernière mise à jour</span>
                            <span class="text-sm text-gray-900">15/01/2024 10:23</span>
                        </div>
                    </div>
                </div>
                
                <!-- Formulaire -->
                <form class="space-y-6">
                    <!-- Traductions groupées par pays -->
                    <div>
                        <h4 class="text-sm font-semibold text-gray-700 uppercase tracking-wider mb-4">Traductions par pays</h4>
                        <div class="space-y-4">
                            <!-- France -->
                            <div class="border border-gray-200 rounded-lg overflow-hidden">
                                <div class="bg-blue-50 px-4 py-2 flex items-center space-x-2">
                                    <span class="text-lg">🇫🇷</span>
                                    <span class="font-medium text-gray-900">France</span>
                                </div>
                                <table class="w-full">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Langue</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Titre</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200">
                                        <tr>
                                            <td class="px-4 py-3 text-sm font-medium text-gray-900">Français</td>
                                            <td class="px-4 py-3">
                                                <input type="text" value="Rendez-vous médecin" class="w-full px-2 py-1 border border-gray-300 rounded text-sm" readonly>
                                            </td>
                                            <td class="px-4 py-3">
                                                <textarea rows="1" class="w-full px-2 py-1 border border-gray-300 rounded text-sm resize-none" readonly>Consultation médicale ou rendez-vous chez un professionnel de santé</textarea>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Suisse -->
                            <div class="border border-gray-200 rounded-lg overflow-hidden">
                                <div class="bg-red-50 px-4 py-2 flex items-center space-x-2">
                                    <span class="text-lg">🇨🇭</span>
                                    <span class="font-medium text-gray-900">Suisse</span>
                                    <span class="text-xs text-gray-500">(Non disponible pour cet événement)</span>
                                </div>
                            </div>
                            
                            <!-- Belgique -->
                            <div class="border border-gray-200 rounded-lg overflow-hidden">
                                <div class="bg-yellow-50 px-4 py-2 flex items-center space-x-2">
                                    <span class="text-lg">🇧🇪</span>
                                    <span class="font-medium text-gray-900">Belgique</span>
                                    <span class="text-xs text-gray-500">(Non disponible pour cet événement)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Récurrence -->
                    <div>
                        <h4 class="text-sm font-semibold text-gray-700 uppercase tracking-wider mb-4">Règle de récurrence</h4>
                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">RRULE</label>
                                <input type="text" value="FREQ=MONTHLY;INTERVAL=3" class="w-full px-3 py-2 border border-gray-300 rounded-lg font-mono text-sm" readonly>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Description lisible</label>
                                <input type="text" value="Tous les 3 mois" class="w-full px-3 py-2 border border-gray-300 rounded-lg" readonly>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Rappels par défaut -->
                    <div>
                        <h4 class="text-sm font-semibold text-gray-700 uppercase tracking-wider mb-4">Rappels par défaut</h4>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <span class="text-sm text-gray-700">1 jour avant</span>
                                <span class="text-xs text-gray-500">-1440 min</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <span class="text-sm text-gray-700">2 heures avant</span>
                                <span class="text-xs text-gray-500">-120 min</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <span class="text-sm text-gray-700">30 minutes avant</span>
                                <span class="text-xs text-gray-500">-30 min</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Configuration -->
                    <div>
                        <h4 class="text-sm font-semibold text-gray-700 uppercase tracking-wider mb-4">Configuration</h4>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="text-sm font-medium text-gray-700">État</label>
                                    <p class="text-xs text-gray-500">Actif dans le système</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer" checked disabled>
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>
                            <div class="flex items-center justify-between">
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Mise à jour annuelle requise</label>
                                    <p class="text-xs text-gray-500">Pour les dates variables</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer" disabled>
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Pays disponibles -->
                    <div>
                        <h4 class="text-sm font-semibold text-gray-700 uppercase tracking-wider mb-4">Disponibilité par pays</h4>
                        <div class="grid grid-cols-3 gap-3">
                            <label class="flex items-center space-x-2 p-3 border border-blue-500 bg-blue-50 rounded-lg cursor-pointer">
                                <input type="checkbox" checked disabled class="text-blue-600">
                                <span class="text-sm font-medium">🇫🇷 France</span>
                            </label>
                            <label class="flex items-center space-x-2 p-3 border border-gray-300 rounded-lg cursor-pointer">
                                <input type="checkbox" disabled class="text-blue-600">
                                <span class="text-sm font-medium">🇨🇭 Suisse</span>
                            </label>
                            <label class="flex items-center space-x-2 p-3 border border-gray-300 rounded-lg cursor-pointer">
                                <input type="checkbox" disabled class="text-blue-600">
                                <span class="text-sm font-medium">🇧🇪 Belgique</span>
                            </label>
                        </div>
                    </div>
                </form>
            `;
        }
    </script>
</body>
</html>