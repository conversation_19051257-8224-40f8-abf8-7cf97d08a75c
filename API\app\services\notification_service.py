"""
Service notifications pour Notiflair API

Gestion des notifications et traductions avec fallback automatique.
"""

from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, or_, and_
import logging

from app.models.notification import Notification, NotificationTranslation, NotificationTranslationOverride
from app.models.reference import Country, Language, NotificationType
from app.models.junction import NotificationKeyword
from app.schemas.notification import (
    NotificationResponse, 
    NotificationListResponse, 
    NotificationListItem,
    NotificationCategoryResponse
)
from app.utils.logging import get_logger

logger = get_logger("notification_service")


class NotificationService:
    """Service de gestion des notifications."""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def get_notifications(
        self,
        country_code: Optional[str] = None,
        language_code: Optional[str] = None,
        category: Optional[str] = None,
        active_only: bool = True,
        page: int = 1,
        page_size: int = 20
    ) -> NotificationListResponse:
        """
        Récupère la liste des notifications avec pagination.
        
        Args:
            country_code: Code pays pour filtrage
            language_code: Code langue pour traductions
            category: Catégorie de notification
            active_only: Notifications actives uniquement
            page: Numéro de page
            page_size: Taille de page
            
        Returns:
            NotificationListResponse: Liste paginée
        """
        try:
            # Construction de la requête de base
            query = self.db.query(Notification).options(
                joinedload(Notification.notification_type),
                joinedload(Notification.country)
            )
            
            # Filtrage par statut actif
            if active_only:
                query = query.filter(Notification.ntf_is_active == True)
            
            # Filtrage par pays
            if country_code:
                country = self.db.query(Country).filter(
                    Country.cty_code == country_code.upper()
                ).first()
                if country:
                    query = query.filter(
                        or_(
                            Notification.cty_id == country.cty_id,
                            Notification.cty_id.is_(None)  # Notifications globales
                        )
                    )
            
            # Filtrage par catégorie
            if category:
                query = query.join(NotificationType).filter(
                    NotificationType.ntp_identifier_key == category
                )
            
            # Comptage total
            total = query.count()
            
            # Pagination
            offset = (page - 1) * page_size
            notifications = query.offset(offset).limit(page_size).all()
            
            # Conversion en items de liste avec traductions
            items = []
            for notif in notifications:
                # Récupération de la traduction appropriée
                translation = await self._get_notification_translation(
                    notif.ntf_id,
                    country_code,
                    language_code
                )
                
                item = NotificationListItem(
                    ntf_id=notif.ntf_id,
                    identifier_key=notif.ntf_identifier_key,
                    label=translation.get("label", notif.ntf_identifier_key),
                    description=translation.get("description"),
                    icon=notif.ntf_icon or notif.notification_type.ntp_icon,
                    color=notif.notification_type.ntp_color,
                    category=notif.notification_type.ntp_identifier_key,
                    is_active=notif.ntf_is_active,
                    relevance_score=notif.ntf_relevance_score,
                    usage_count=notif.ntf_usage_count
                )
                items.append(item)
            
            # Calcul des métadonnées de pagination
            total_pages = (total + page_size - 1) // page_size
            has_next = page < total_pages
            has_previous = page > 1
            
            logger.info(
                f"Récupération notifications: {len(items)} items, "
                f"page {page}/{total_pages}"
            )
            
            return NotificationListResponse(
                items=items,
                total=total,
                page=page,
                page_size=page_size,
                total_pages=total_pages,
                has_next=has_next,
                has_previous=has_previous
            )
            
        except Exception as e:
            logger.error(f"Erreur récupération notifications: {str(e)}")
            raise
    
    async def get_notification_by_id(
        self,
        notification_id: int,
        country_code: Optional[str] = None,
        language_code: Optional[str] = None
    ) -> Optional[NotificationResponse]:
        """
        Récupère une notification spécifique avec ses traductions.
        
        Args:
            notification_id: ID de la notification
            country_code: Code pays
            language_code: Code langue
            
        Returns:
            NotificationResponse: Notification complète ou None
        """
        try:
            notification = self.db.query(Notification).options(
                joinedload(Notification.notification_type),
                joinedload(Notification.country),
                joinedload(Notification.translations),
                joinedload(Notification.translation_overrides)
            ).filter(Notification.ntf_id == notification_id).first()
            
            if not notification:
                return None
            
            # TODO: Implémenter la conversion complète en NotificationResponse
            # avec toutes les traductions et métadonnées
            
            logger.info(f"Récupération notification: {notification_id}")
            return None  # Placeholder
            
        except Exception as e:
            logger.error(f"Erreur récupération notification {notification_id}: {str(e)}")
            raise
    
    async def search_notifications(
        self,
        query: str,
        country_code: Optional[str] = None,
        language_code: Optional[str] = None,
        page: int = 1,
        page_size: int = 20
    ) -> Dict[str, Any]:
        """
        Recherche de notifications par mots-clés.
        
        Args:
            query: Terme de recherche
            country_code: Code pays
            language_code: Code langue
            page: Numéro de page
            page_size: Taille de page
            
        Returns:
            Dict: Résultats de recherche
        """
        try:
            # TODO: Implémenter recherche full-text
            # avec score de pertinence et recherche dans traductions
            
            logger.info(f"Recherche notifications: '{query}'")
            return {
                "items": [],
                "total": 0,
                "page": page,
                "page_size": page_size,
                "query": query
            }
            
        except Exception as e:
            logger.error(f"Erreur recherche notifications: {str(e)}")
            raise
    
    async def get_notification_categories(
        self,
        country_code: Optional[str] = None,
        language_code: Optional[str] = None
    ) -> List[NotificationCategoryResponse]:
        """
        Récupère les catégories de notifications.
        
        Args:
            country_code: Code pays
            language_code: Code langue
            
        Returns:
            List[NotificationCategoryResponse]: Liste des catégories
        """
        try:
            categories = self.db.query(NotificationType).order_by(
                NotificationType.ntp_display_order
            ).all()
            
            result = []
            for category in categories:
                # Comptage des notifications dans cette catégorie
                notif_count = self.db.query(func.count(Notification.ntf_id)).filter(
                    Notification.ntp_id == category.ntp_id,
                    Notification.ntf_is_active == True
                ).scalar()
                
                # TODO: Récupérer traduction de la catégorie
                
                item = NotificationCategoryResponse(
                    identifier_key=category.ntp_identifier_key,
                    label=category.ntp_identifier_key,  # TODO: traduction
                    icon=category.ntp_icon,
                    color=category.ntp_color,
                    notification_count=notif_count or 0,
                    level=category.ntp_level,
                    parent_key=None  # TODO: gérer hiérarchie
                )
                result.append(item)
            
            logger.info(f"Récupération catégories: {len(result)} items")
            return result
            
        except Exception as e:
            logger.error(f"Erreur récupération catégories: {str(e)}")
            raise
    
    async def increment_usage_count(self, notification_id: int) -> bool:
        """
        Incrémente le compteur d'utilisation d'une notification.
        
        Args:
            notification_id: ID de la notification
            
        Returns:
            bool: Succès de l'opération
        """
        try:
            notification = self.db.query(Notification).filter(
                Notification.ntf_id == notification_id
            ).first()
            
            if not notification:
                raise ValueError(f"Notification {notification_id} non trouvée")
            
            notification.ntf_usage_count = (notification.ntf_usage_count or 0) + 1
            self.db.commit()
            
            logger.info(f"Usage count incrémenté: {notification_id}")
            return True
            
        except Exception as e:
            logger.error(f"Erreur incrément usage {notification_id}: {str(e)}")
            self.db.rollback()
            raise
    
    async def _get_notification_translation(
        self,
        notification_id: int,
        country_code: Optional[str] = None,
        language_code: Optional[str] = None
    ) -> Dict[str, Optional[str]]:
        """
        Récupère la traduction d'une notification avec fallback automatique.
        
        Ordre de priorité:
        1. Override pays/langue spécifique
        2. Traduction par défaut dans la langue
        3. Traduction anglaise par défaut
        4. Clé technique
        
        Args:
            notification_id: ID de la notification
            country_code: Code pays
            language_code: Code langue
            
        Returns:
            Dict: Traduction avec label et description
        """
        try:
            result = {"label": None, "description": None}
            
            # Récupération des IDs pays/langue
            country_id = None
            language_id = None
            
            if country_code:
                country = self.db.query(Country).filter(
                    Country.cty_code == country_code.upper()
                ).first()
                if country:
                    country_id = country.cty_id
            
            if language_code:
                language = self.db.query(Language).filter(
                    Language.lng_code == language_code.lower()
                ).first()
                if language:
                    language_id = language.lng_id
            
            # 1. Tentative override pays/langue
            if country_id and language_id:
                override = self.db.query(NotificationTranslationOverride).filter(
                    NotificationTranslationOverride.ntf_id == notification_id,
                    NotificationTranslationOverride.cty_id == country_id,
                    NotificationTranslationOverride.lng_id == language_id
                ).first()
                
                if override:
                    result["label"] = override.nov_label
                    result["description"] = override.nov_description
                    return result
            
            # 2. Traduction par défaut dans la langue
            if language_id:
                translation = self.db.query(NotificationTranslation).filter(
                    NotificationTranslation.ntf_id == notification_id,
                    NotificationTranslation.lng_id == language_id
                ).first()
                
                if translation:
                    result["label"] = translation.ntr_label
                    result["description"] = translation.ntr_description
                    return result
            
            # 3. Fallback vers anglais
            english = self.db.query(Language).filter(
                Language.lng_code == "en"
            ).first()
            
            if english:
                translation = self.db.query(NotificationTranslation).filter(
                    NotificationTranslation.ntf_id == notification_id,
                    NotificationTranslation.lng_id == english.lng_id
                ).first()
                
                if translation:
                    result["label"] = translation.ntr_label
                    result["description"] = translation.ntr_description
                    return result
            
            # 4. Fallback vers clé technique
            notification = self.db.query(Notification).filter(
                Notification.ntf_id == notification_id
            ).first()
            
            if notification:
                result["label"] = notification.ntf_identifier_key
            
            return result
            
        except Exception as e:
            logger.error(f"Erreur récupération traduction {notification_id}: {str(e)}")
            return {"label": f"notification_{notification_id}", "description": None}
