"""
Schémas Pydantic pour les traductions d'application

Schémas de validation et sérialisation pour les endpoints de traductions d'app.
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


class AppTranslationResponse(BaseModel):
    """
    Schéma de réponse pour une clé de traduction d'application
    """
    atr_id: int = Field(..., description="ID unique de la clé de traduction")
    atr_key: str = Field(..., description="Clé de traduction")
    atr_context: Optional[str] = Field(None, description="Contexte d'utilisation")
    
    class Config:
        from_attributes = True


class AppTranslationValueResponse(BaseModel):
    """
    Schéma de réponse pour une valeur de traduction d'application ⭐ STRUCTURE CORRECTE
    
    Traductions par LANGUE uniquement (structure déjà correcte).
    """
    atr_id: int = Field(..., description="ID de la clé de traduction")
    lng_id: int = Field(..., description="ID de la langue")
    language_code: str = Field(..., description="Code de la langue")
    atv_value: str = Field(..., description="Valeur traduite")
    translation_source: str = Field(..., description="Source de la traduction (default/override/fallback)")
    
    class Config:
        from_attributes = True


class AppTranslationOverrideResponse(BaseModel):
    """
    Schéma de réponse pour un override de traduction d'application ⭐ NOUVEAU
    
    Overrides par PAYS-LANGUE (post-correction 2025).
    """
    ato_id: int = Field(..., description="ID unique de l'override")
    atr_id: int = Field(..., description="ID de la clé de traduction")
    ctl_id: int = Field(..., description="ID de la combinaison pays-langue")
    country_code: str = Field(..., description="Code du pays")
    language_code: str = Field(..., description="Code de la langue")
    ato_value: str = Field(..., description="Valeur override")
    
    class Config:
        from_attributes = True


class AppTranslationWithValuesResponse(BaseModel):
    """
    Schéma de réponse pour une clé de traduction avec ses valeurs
    """
    atr_id: int = Field(..., description="ID unique de la clé de traduction")
    atr_key: str = Field(..., description="Clé de traduction")
    atr_context: Optional[str] = Field(None, description="Contexte d'utilisation")
    values: List[AppTranslationValueResponse] = Field(default=[], description="Valeurs par langue")
    overrides: List[AppTranslationOverrideResponse] = Field(default=[], description="Overrides par pays-langue")
    
    class Config:
        from_attributes = True


class AppTranslationsListResponse(BaseModel):
    """
    Schéma de réponse pour la liste des traductions d'application
    """
    translations: List[AppTranslationWithValuesResponse] = Field(..., description="Liste des traductions")
    total: int = Field(..., description="Nombre total de traductions")
    
    class Config:
        from_attributes = True
