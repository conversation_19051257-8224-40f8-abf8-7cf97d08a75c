<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Schéma Base de Données Notiflair - Vue Complète</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.2em;
        }

        .legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px 15px;
            border-radius: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
        }

        .schema-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .table-group {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .table-group:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }

        .group-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 3px solid;
        }

        .group-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            font-weight: bold;
        }

        .group-title {
            font-size: 1.4em;
            font-weight: 600;
            color: #2c3e50;
        }

        .table-item {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }

        .table-item:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }

        .table-name {
            font-weight: 600;
            font-size: 1.1em;
            margin-bottom: 5px;
            color: #2c3e50;
        }

        .table-description {
            color: #6c757d;
            font-size: 0.9em;
            line-height: 1.4;
        }

        .table-fields {
            margin-top: 10px;
            font-size: 0.85em;
            color: #495057;
        }

        .field-count {
            background: #e9ecef;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            color: #6c757d;
        }

        /* Couleurs par groupe */
        .reference {
            border-color: #3498db;
        }

        .reference .group-icon {
            background: #3498db;
        }

        .reference .group-header {
            border-color: #3498db;
        }

        .reference .table-item {
            border-left-color: #3498db;
        }

        .notifications {
            border-color: #e74c3c;
        }

        .notifications .group-icon {
            background: #e74c3c;
        }

        .notifications .group-header {
            border-color: #e74c3c;
        }

        .notifications .table-item {
            border-left-color: #e74c3c;
        }

        .translations {
            border-color: #f39c12;
        }

        .translations .group-icon {
            background: #f39c12;
        }

        .translations .group-header {
            border-color: #f39c12;
        }

        .translations .table-item {
            border-left-color: #f39c12;
        }

        .overrides {
            border-color: #9b59b6;
        }

        .overrides .group-icon {
            background: #9b59b6;
        }

        .overrides .group-header {
            border-color: #9b59b6;
        }

        .overrides .table-item {
            border-left-color: #9b59b6;
        }

        .app-translations {
            border-color: #1abc9c;
        }

        .app-translations .group-icon {
            background: #1abc9c;
        }

        .app-translations .group-header {
            border-color: #1abc9c;
        }

        .app-translations .table-item {
            border-left-color: #1abc9c;
        }

        .associations {
            border-color: #34495e;
        }

        .associations .group-icon {
            background: #34495e;
        }

        .associations .group-header {
            border-color: #34495e;
        }

        .associations .table-item {
            border-left-color: #34495e;
        }

        .stats {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .stats h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
        }

        .stat-item {
            padding: 15px;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .schema-grid {
                grid-template-columns: 1fr;
            }

            .legend {
                flex-direction: column;
                align-items: center;
            }

            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ Schéma Base de Données Notiflair</h1>
            <p>Architecture complète après migration 2025 - Vue interactive</p>
        </div>

        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background: #3498db;"></div>
                <span>Tables de Référence</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #e74c3c;"></div>
                <span>Notifications</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #f39c12;"></div>
                <span>Traductions</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #9b59b6;"></div>
                <span>Overrides</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #1abc9c;"></div>
                <span>App Translations</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #34495e;"></div>
                <span>Associations</span>
            </div>
        </div>

        <div class="schema-grid">
            <!-- Tables de Référence -->
            <div class="table-group reference">
                <div class="group-header">
                    <div class="group-icon">🌍</div>
                    <div class="group-title">Tables de Référence</div>
                </div>

                <div class="table-item">
                    <div class="table-name">tr_countries_cty</div>
                    <div class="table-description">Pays supportés par l'application</div>
                    <div class="table-fields">
                        <span class="field-count">6 champs</span> • cty_id, cty_code, cty_name, cty_timezone,
                        cty_default_lng_id, cty_created_at
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-name">tr_languages_lng</div>
                    <div class="table-description">Langues supportées</div>
                    <div class="table-fields">
                        <span class="field-count">4 champs</span> • lng_id, lng_code, lng_name, lng_created_at
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-name">tr_countries_languages_ctl</div>
                    <div class="table-description">Pivot central pays-langue (cœur de l'architecture)</div>
                    <div class="table-fields">
                        <span class="field-count">4 champs</span> • ctl_id, cty_id, lng_id, ctl_created_at
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-name">tr_keywords_kwd</div>
                    <div class="table-description">Mots-clés pour catégoriser les notifications</div>
                    <div class="table-fields">
                        <span class="field-count">3 champs</span> • kwd_id, kwd_identifier, kwd_created_at
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-name">tr_notif_types_ntp</div>
                    <div class="table-description">Types de notifications (structure hiérarchique)</div>
                    <div class="table-fields">
                        <span class="field-count">9 champs</span> • ntp_id, ntp_identifier_key, ntp_icon, ntp_color,
                        ntp_parent_id, ntp_level, etc.
                    </div>
                </div>
            </div>

            <!-- Notifications -->
            <div class="table-group notifications">
                <div class="group-header">
                    <div class="group-icon">🔔</div>
                    <div class="group-title">Notifications</div>
                </div>

                <div class="table-item">
                    <div class="table-name">t_notifications_ntf</div>
                    <div class="table-description">Notifications principales avec métadonnées</div>
                    <div class="table-fields">
                        <span class="field-count">12 champs</span> • ntf_id, ntf_identifier_key, ntp_id, cty_id,
                        ntf_is_active, ntf_priority, etc.
                    </div>
                </div>
            </div>

            <!-- Traductions de Base -->
            <div class="table-group translations">
                <div class="group-header">
                    <div class="group-icon">🌐</div>
                    <div class="group-title">Traductions de Base</div>
                </div>

                <div class="table-item">
                    <div class="table-name">t_ntf_translations_ntr</div>
                    <div class="table-description">Traductions des notifications par langue</div>
                    <div class="table-fields">
                        <span class="field-count">7 champs</span> • ntr_id, ntf_id, lng_id, ntr_label, ntr_description,
                        ntr_created_at, ntr_updated_at
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-name">t_ntp_translations_ntt</div>
                    <div class="table-description">Traductions des types de notifications par langue</div>
                    <div class="table-fields">
                        <span class="field-count">7 champs</span> • ntt_id, ntp_id, lng_id, ntt_label, ntt_description,
                        ntt_created_at, ntt_updated_at
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-name">t_nkw_translations_kwt</div>
                    <div class="table-description">Traductions des mots-clés par langue</div>
                    <div class="table-fields">
                        <span class="field-count">3 champs</span> • kwd_id, lng_id, kwt_term
                    </div>
                </div>
            </div>

            <!-- Overrides Locaux -->
            <div class="table-group overrides">
                <div class="group-header">
                    <div class="group-icon">🎯</div>
                    <div class="group-title">Overrides Locaux</div>
                </div>

                <div class="table-item">
                    <div class="table-name">t_ntf_translations_override_nov</div>
                    <div class="table-description">Overrides spécifiques par pays-langue pour notifications</div>
                    <div class="table-fields">
                        <span class="field-count">7 champs</span> • nov_id, ntr_id, ctl_id, nov_label, nov_description,
                        nov_created_at, nov_updated_at
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-name">t_ntp_translations_override_nto</div>
                    <div class="table-description">Overrides spécifiques par pays-langue pour types</div>
                    <div class="table-fields">
                        <span class="field-count">7 champs</span> • nto_id, ntt_id, ctl_id, nto_label, nto_description,
                        nto_created_at, nto_updated_at
                    </div>
                </div>
            </div>

            <!-- Traductions Application -->
            <div class="table-group app-translations">
                <div class="group-header">
                    <div class="group-icon">📱</div>
                    <div class="group-title">Traductions Application</div>
                </div>

                <div class="table-item">
                    <div class="table-name">t_atr_translations_atv</div>
                    <div class="table-description">Traductions de l'interface utilisateur</div>
                    <div class="table-fields">
                        <span class="field-count">4 champs</span> • atr_id, atr_key, atr_context, atr_created_at
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-name">t_atv_override_ato</div>
                    <div class="table-description">Overrides des traductions d'interface par pays-langue</div>
                    <div class="table-fields">
                        <span class="field-count">6 champs</span> • ato_id, atr_id, ctl_id, ato_value, ato_created_at,
                        ato_updated_at
                    </div>
                </div>
            </div>

            <!-- Tables d'Association -->
            <div class="table-group associations">
                <div class="group-header">
                    <div class="group-icon">🔗</div>
                    <div class="group-title">Tables d'Association</div>
                </div>

                <div class="table-item">
                    <div class="table-name">tj_ntf_keywords_nkw</div>
                    <div class="table-description">Association many-to-many notifications ↔ mots-clés</div>
                    <div class="table-fields">
                        <span class="field-count">2 champs</span> • ntf_id, kwd_id
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistiques -->
        <div class="stats">
            <h3>📊 Statistiques du Schéma</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">13</div>
                    <div class="stat-label">Tables Total</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">5</div>
                    <div class="stat-label">Tables Référence</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">3</div>
                    <div class="stat-label">Tables Traductions</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">2</div>
                    <div class="stat-label">Tables Overrides</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">2</div>
                    <div class="stat-label">Tables App</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">1</div>
                    <div class="stat-label">Table Association</div>
                </div>
            </div>
        </div>

        <!-- Architecture Info -->
        <div
            style="margin-top: 30px; background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 25px; box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);">
            <h3 style="color: #2c3e50; margin-bottom: 15px; text-align: center;">🏗️ Architecture Post-Migration 2025
            </h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; border-left: 4px solid #3498db;">
                    <h4 style="color: #3498db; margin-bottom: 10px;">🎯 Pivot Central</h4>
                    <p style="color: #6c757d; font-size: 0.9em;">La table <strong>tr_countries_languages_ctl</strong>
                        est le cœur de l'architecture. Chaque combinaison pays-langue a un identifiant unique (ctl_id).
                    </p>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; border-left: 4px solid #f39c12;">
                    <h4 style="color: #f39c12; margin-bottom: 10px;">🌐 Traductions Universelles</h4>
                    <p style="color: #6c757d; font-size: 0.9em;">Les traductions de base utilisent
                        <strong>lng_id</strong> (langue uniquement) pour des traductions universelles par langue.</p>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; border-left: 4px solid #9b59b6;">
                    <h4 style="color: #9b59b6; margin-bottom: 10px;">🎯 Overrides Locaux</h4>
                    <p style="color: #6c757d; font-size: 0.9em;">Les overrides utilisent <strong>ctl_id</strong> pour
                        des spécificités locales par combinaison pays-langue.</p>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; border-left: 4px solid #e74c3c;">
                    <h4 style="color: #e74c3c; margin-bottom: 10px;">🔄 Logique de Fallback</h4>
                    <p style="color: #6c757d; font-size: 0.9em;">Override → Traduction par défaut → Clé technique.
                        Garantit toujours un affichage même sans traduction.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Animation d'entrée
        document.addEventListener('DOMContentLoaded', function () {
            const tableGroups = document.querySelectorAll('.table-group');
            tableGroups.forEach((group, index) => {
                group.style.opacity = '0';
                group.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    group.style.transition = 'all 0.6s ease';
                    group.style.opacity = '1';
                    group.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });

        // Effet de survol interactif
        document.querySelectorAll('.table-item').forEach(item => {
            item.addEventListener('mouseenter', function () {
                this.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.1)';
            });

            item.addEventListener('mouseleave', function () {
                this.style.boxShadow = 'none';
            });
        });
    </script>
</body>

</html>