"""
Service synchronisation pour Notiflair API

Gestion des snapshots et synchronisation mobile.
"""

from typing import Optional, Dict, Any, List
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import func
import json
import gzip
import hashlib
import logging

from app.models.sync import SyncSnapshot
from app.models.translation import TranslationVersion
from app.models.reference import Country, Language
from app.models.user import User
from app.schemas.sync import (
    VersionCheckResponse,
    SnapshotResponse,
    SyncStatusResponse,
    CountryResponse,
    LanguageResponse
)
from app.utils.logging import get_logger

logger = get_logger("sync_service")


class SyncService:
    """Service de synchronisation et gestion des snapshots."""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def check_version(
        self,
        country_code: str,
        language_code: str,
        client_version: Optional[str] = None
    ) -> VersionCheckResponse:
        """
        Vérifie si une mise à jour est disponible.
        
        Args:
            country_code: Code pays
            language_code: Code langue
            client_version: Version actuelle du client
            
        Returns:
            VersionCheckResponse: Informations de version
        """
        try:
            # Récupération des IDs pays/langue
            country = self.db.query(Country).filter(
                Country.cty_code == country_code.upper()
            ).first()
            
            language = self.db.query(Language).filter(
                Language.lng_code == language_code.lower()
            ).first()
            
            if not country or not language:
                raise ValueError(f"Pays/langue non supporté: {country_code}/{language_code}")
            
            # Récupération de la version serveur
            version_info = self.db.query(TranslationVersion).filter(
                TranslationVersion.cty_id == country.cty_id,
                TranslationVersion.lng_id == language.lng_id
            ).first()
            
            if not version_info:
                # Création d'une version par défaut
                version_info = TranslationVersion(
                    cty_id=country.cty_id,
                    lng_id=language.lng_id,
                    tvr_current_version="v1.0.0",
                    tvr_has_pending_changes=False,
                    tvr_total_items=0
                )
                self.db.add(version_info)
                self.db.commit()
                self.db.refresh(version_info)
            
            server_version = version_info.tvr_current_version
            
            # Comparaison des versions
            update_available = False
            update_required = False
            
            if client_version and client_version != server_version:
                update_available = True
                # TODO: Implémenter logique de version obligatoire
            
            # Vérification de l'existence du snapshot
            snapshot = self.db.query(SyncSnapshot).filter(
                SyncSnapshot.cty_id == country.cty_id,
                SyncSnapshot.lng_id == language.lng_id,
                SyncSnapshot.sss_version == server_version
            ).first()
            
            snapshot_available = snapshot is not None
            snapshot_size = snapshot.sss_size_compressed if snapshot else None
            snapshot_checksum = snapshot.sss_checksum if snapshot else None
            
            logger.info(
                f"Vérification version: {country_code}/{language_code} "
                f"client={client_version} server={server_version} "
                f"update={update_available}"
            )
            
            return VersionCheckResponse(
                server_version=server_version,
                client_version=client_version,
                update_available=update_available,
                update_required=update_required,
                snapshot_available=snapshot_available,
                snapshot_size=snapshot_size,
                snapshot_checksum=snapshot_checksum,
                last_updated=version_info.tvr_last_published,
                changelog=None  # TODO: Implémenter changelog
            )
            
        except Exception as e:
            logger.error(f"Erreur vérification version: {str(e)}")
            raise
    
    async def get_snapshot(
        self,
        country_code: str,
        language_code: str,
        version: Optional[str] = None,
        compressed: bool = True
    ) -> Optional[SnapshotResponse]:
        """
        Récupère un snapshot de données.
        
        Args:
            country_code: Code pays
            language_code: Code langue
            version: Version spécifique (dernière si None)
            compressed: Retourner en format compressé
            
        Returns:
            SnapshotResponse: Données du snapshot ou None
        """
        try:
            # Récupération des IDs pays/langue
            country = self.db.query(Country).filter(
                Country.cty_code == country_code.upper()
            ).first()
            
            language = self.db.query(Language).filter(
                Language.lng_code == language_code.lower()
            ).first()
            
            if not country or not language:
                return None
            
            # Construction de la requête snapshot
            query = self.db.query(SyncSnapshot).filter(
                SyncSnapshot.cty_id == country.cty_id,
                SyncSnapshot.lng_id == language.lng_id
            )
            
            if version:
                query = query.filter(SyncSnapshot.sss_version == version)
            else:
                # Récupération de la dernière version
                query = query.order_by(SyncSnapshot.sss_created_at.desc())
            
            snapshot = query.first()
            
            if not snapshot:
                return None
            
            # Décompression des données si nécessaire
            data = None
            if not compressed and snapshot.sss_data:
                try:
                    decompressed = gzip.decompress(snapshot.sss_data)
                    data = json.loads(decompressed.decode('utf-8'))
                except Exception as e:
                    logger.error(f"Erreur décompression snapshot: {str(e)}")
            
            logger.info(
                f"Récupération snapshot: {country_code}/{language_code} "
                f"v{snapshot.sss_version} size={snapshot.sss_size_compressed}B"
            )
            
            return SnapshotResponse(
                version=snapshot.sss_version,
                country_code=country_code,
                language_code=language_code,
                generated_at=snapshot.sss_created_at,
                checksum=snapshot.sss_checksum,
                size_bytes=snapshot.sss_size_bytes,
                size_compressed=snapshot.sss_size_compressed,
                item_count=snapshot.sss_item_count,
                data=data,
                download_url=f"/api/v1/sync/snapshot/download?country_code={country_code}&language_code={language_code}&version={snapshot.sss_version}"
            )
            
        except Exception as e:
            logger.error(f"Erreur récupération snapshot: {str(e)}")
            raise
    
    async def get_snapshot_binary(
        self,
        country_code: str,
        language_code: str,
        version: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Récupère les données binaires d'un snapshot.
        
        Args:
            country_code: Code pays
            language_code: Code langue
            version: Version spécifique
            
        Returns:
            Dict: Données binaires et métadonnées
        """
        try:
            # Récupération du snapshot
            snapshot_response = await self.get_snapshot(
                country_code, language_code, version, compressed=True
            )
            
            if not snapshot_response:
                return None
            
            # Récupération des données binaires depuis la DB
            country = self.db.query(Country).filter(
                Country.cty_code == country_code.upper()
            ).first()
            
            language = self.db.query(Language).filter(
                Language.lng_code == language_code.lower()
            ).first()
            
            snapshot = self.db.query(SyncSnapshot).filter(
                SyncSnapshot.cty_id == country.cty_id,
                SyncSnapshot.lng_id == language.lng_id,
                SyncSnapshot.sss_version == snapshot_response.version
            ).first()
            
            if not snapshot or not snapshot.sss_data:
                return None
            
            return {
                "data": snapshot.sss_data,
                "version": snapshot.sss_version,
                "checksum": snapshot.sss_checksum,
                "size": len(snapshot.sss_data)
            }
            
        except Exception as e:
            logger.error(f"Erreur récupération snapshot binaire: {str(e)}")
            raise
    
    async def update_user_sync(self, user_id: int) -> bool:
        """
        Met à jour la date de synchronisation d'un utilisateur.
        
        Args:
            user_id: ID de l'utilisateur
            
        Returns:
            bool: Succès de l'opération
        """
        try:
            user = self.db.query(User).filter(User.usr_id == user_id).first()
            
            if not user:
                return False
            
            user.usr_last_sync = datetime.utcnow()
            self.db.commit()
            
            return True
            
        except Exception as e:
            logger.error(f"Erreur mise à jour sync utilisateur {user_id}: {str(e)}")
            self.db.rollback()
            raise
    
    async def get_user_sync_status(self, user_id: int) -> SyncStatusResponse:
        """
        Récupère le statut de synchronisation d'un utilisateur.
        
        Args:
            user_id: ID de l'utilisateur
            
        Returns:
            SyncStatusResponse: Statut de synchronisation
        """
        try:
            user = self.db.query(User).filter(User.usr_id == user_id).first()
            
            if not user:
                raise ValueError(f"Utilisateur {user_id} non trouvé")
            
            # Comptage des synchronisations (approximatif)
            sync_count = 1 if user.usr_last_sync else 0
            
            # Récupération des versions disponibles
            available_versions = []
            versions = self.db.query(TranslationVersion).all()
            
            for version in versions:
                country = self.db.query(Country).filter(
                    Country.cty_id == version.cty_id
                ).first()
                language = self.db.query(Language).filter(
                    Language.lng_id == version.lng_id
                ).first()
                
                if country and language:
                    available_versions.append({
                        "country_code": country.cty_code,
                        "language_code": language.lng_code,
                        "version": version.tvr_current_version,
                        "last_updated": version.tvr_last_published.isoformat() if version.tvr_last_published else None
                    })
            
            return SyncStatusResponse(
                user_id=user.usr_id,
                device_id=user.usr_device_id,
                last_sync=user.usr_last_sync,
                sync_count=sync_count,
                available_versions=available_versions,
                pending_updates=[]  # TODO: Calculer les mises à jour en attente
            )
            
        except Exception as e:
            logger.error(f"Erreur statut sync utilisateur {user_id}: {str(e)}")
            raise
    
    async def get_available_countries(self) -> List[CountryResponse]:
        """
        Récupère la liste des pays disponibles.
        
        Returns:
            List[CountryResponse]: Liste des pays
        """
        try:
            countries = self.db.query(Country).order_by(Country.cty_name).all()
            
            result = []
            for country in countries:
                # Récupération des langues disponibles pour ce pays
                # TODO: Implémenter via table de jointure
                
                item = CountryResponse(
                    cty_id=country.cty_id,
                    cty_code=country.cty_code,
                    cty_name=country.cty_name,
                    cty_timezone=country.cty_timezone,
                    default_language_code="fr",  # TODO: récupérer depuis relation
                    available_languages=[]  # TODO: récupérer langues disponibles
                )
                result.append(item)
            
            logger.info(f"Récupération pays disponibles: {len(result)} items")
            return result
            
        except Exception as e:
            logger.error(f"Erreur récupération pays: {str(e)}")
            raise
    
    async def get_available_languages(
        self, 
        country_code: Optional[str] = None
    ) -> List[LanguageResponse]:
        """
        Récupère la liste des langues disponibles.
        
        Args:
            country_code: Filtrer par pays
            
        Returns:
            List[LanguageResponse]: Liste des langues
        """
        try:
            query = self.db.query(Language).order_by(Language.lng_name)
            
            # TODO: Filtrage par pays via table de jointure
            
            languages = query.all()
            
            result = []
            for language in languages:
                item = LanguageResponse(
                    lng_id=language.lng_id,
                    lng_code=language.lng_code,
                    lng_name=language.lng_name,
                    available_countries=[]  # TODO: récupérer pays disponibles
                )
                result.append(item)
            
            logger.info(f"Récupération langues disponibles: {len(result)} items")
            return result
            
        except Exception as e:
            logger.error(f"Erreur récupération langues: {str(e)}")
            raise
