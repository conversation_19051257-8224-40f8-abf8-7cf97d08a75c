"""
Service d'authentification pour l'admin Notiflair

Ce service gère l'authentification des utilisateurs admin en utilisant
un fichier JSON temporaire. Il sera migré vers une base de données plus tard.
"""

import json
import os
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Union
from pathlib import Path

from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status
from pydantic import BaseModel

from app.config import settings


class AdminUser(BaseModel):
    """Modèle pour un utilisateur admin."""
    id: int
    username: str
    email: str
    hashed_password: str
    full_name: str
    is_active: bool = True
    is_superuser: bool = False
    roles: List[str] = []
    created_at: str
    last_login: Optional[str] = None


class AdminUserInDB(AdminUser):
    """Utilisateur admin avec mot de passe hashé."""
    pass


class AdminUserPublic(BaseModel):
    """Utilisateur admin pour les réponses publiques (sans mot de passe)."""
    id: int
    username: str
    email: str
    full_name: str
    is_active: bool
    is_superuser: bool
    roles: List[str]
    created_at: str
    last_login: Optional[str] = None


class AuthService:
    """Service d'authentification pour les admins."""
    
    def __init__(self):
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        self.credentials_file = Path("data/admin_credentials.json")
        self._users_cache: Optional[Dict[str, AdminUser]] = None
        self._last_load_time: Optional[datetime] = None
        
    def _load_credentials(self) -> Dict[str, AdminUser]:
        """Charge les credentials depuis le fichier JSON."""
        try:
            # Vérifier si le fichier existe
            if not self.credentials_file.exists():
                raise FileNotFoundError(f"Fichier credentials non trouvé: {self.credentials_file}")
            
            # Vérifier si on doit recharger (cache de 5 minutes)
            if (self._users_cache is not None and 
                self._last_load_time is not None and 
                datetime.now() - self._last_load_time < timedelta(minutes=5)):
                return self._users_cache
            
            # Charger le fichier
            with open(self.credentials_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Convertir en dictionnaire indexé par username
            users = {}
            for user_data in data.get("users", []):
                user = AdminUser(**user_data)
                users[user.username] = user
                users[user.email] = user  # Permettre login par email aussi
            
            # Mettre en cache
            self._users_cache = users
            self._last_load_time = datetime.now()
            
            return users
            
        except FileNotFoundError:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Configuration d'authentification non trouvée"
            )
        except json.JSONDecodeError as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Erreur de format dans le fichier credentials: {e}"
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Erreur lors du chargement des credentials: {e}"
            )
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Vérifie un mot de passe contre son hash."""
        return self.pwd_context.verify(plain_password, hashed_password)
    
    def get_password_hash(self, password: str) -> str:
        """Génère le hash d'un mot de passe."""
        return self.pwd_context.hash(password)
    
    def authenticate_user(self, username: str, password: str) -> Optional[AdminUser]:
        """Authentifie un utilisateur avec username/email et mot de passe."""
        users = self._load_credentials()
        user = users.get(username)
        
        if not user:
            return None
        
        if not user.is_active:
            return None
            
        if not self.verify_password(password, user.hashed_password):
            return None
            
        return user
    
    def get_user(self, username: str) -> Optional[AdminUser]:
        """Récupère un utilisateur par son username ou email."""
        users = self._load_credentials()
        return users.get(username)
    
    def create_access_token(self, data: dict, expires_delta: Optional[timedelta] = None) -> str:
        """Crée un token JWT d'accès."""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        return encoded_jwt
    
    def verify_token(self, token: str) -> Optional[Dict]:
        """Vérifie et décode un token JWT."""
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
            username: str = payload.get("sub")
            if username is None:
                return None
            return payload
        except JWTError:
            return None
    
    def update_last_login(self, username: str) -> None:
        """Met à jour la date de dernière connexion (en mémoire seulement pour le JSON)."""
        # Pour l'instant, on ne persiste pas dans le fichier JSON
        # Cette fonctionnalité sera implémentée avec la base de données
        pass
    
    def get_all_users(self) -> List[AdminUserPublic]:
        """Récupère tous les utilisateurs (sans mots de passe)."""
        users = self._load_credentials()
        # Dédupliquer (car on indexe par username ET email)
        unique_users = {}
        for user in users.values():
            if user.username not in unique_users:
                unique_users[user.username] = AdminUserPublic(
                    id=user.id,
                    username=user.username,
                    email=user.email,
                    full_name=user.full_name,
                    is_active=user.is_active,
                    is_superuser=user.is_superuser,
                    roles=user.roles,
                    created_at=user.created_at,
                    last_login=user.last_login
                )
        return list(unique_users.values())


# Instance globale du service d'authentification
auth_service = AuthService()


def get_auth_service() -> AuthService:
    """Dependency injection pour le service d'auth."""
    return auth_service
