# =============================================================================
# NOTIFLAIR API - DEPENDENCIES
# Versions optimisees pour Python 3.11 (LTS recommande)
# =============================================================================

# FastAPI Core - Versions stables et performantes
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0

# Database - Stack SQLAlchemy moderne
sqlalchemy==2.0.23
pymysql==1.1.0
cryptography==41.0.7
alembic==1.12.1

# Authentication & Security - JWT et hashing
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# HTTP & Validation - Pydantic v2 moderne
pydantic==2.5.0
pydantic-settings==2.1.0
requests==2.31.0
httpx==0.25.2

# Configuration & Environment
python-dotenv==1.0.0

# Data Processing - Versions compatibles Python 3.11
pandas==2.1.4
numpy==1.25.2

# Date & Time
python-dateutil==2.8.2

# Testing - Suite complete pytest
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0

# Development Tools - Formatage et qualite code
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Monitoring & Logging - Logs structures
structlog==23.2.0

# Utilities - CLI et affichage
click==8.1.7
rich==13.7.0
typer==0.9.0

# Redis - Cache haute performance (optionnel)
redis==5.0.1
hiredis==2.2.3

# Email - Notifications (optionnel)
# emails==0.6.0
# jinja2==3.1.2

# Monitoring - Production (optionnel)
# sentry-sdk[fastapi]==1.38.0
# prometheus-client==0.19.0
