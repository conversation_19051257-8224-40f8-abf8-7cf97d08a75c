{"version": "2.0.0", "tasks": [{"label": "Install API Dependencies", "type": "shell", "command": "pip", "args": ["install", "-r", "requirements.txt"], "options": {"cwd": "${workspaceFolder}/API"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Start FastAPI Dev Server", "type": "shell", "command": "u<PERSON><PERSON>", "args": ["main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"], "options": {"cwd": "${workspaceFolder}/API"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Run API Tests", "type": "shell", "command": "pytest", "args": ["-v", "tests/"], "options": {"cwd": "${workspaceFolder}/API"}, "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Format Python Code", "type": "shell", "command": "black", "args": ["."], "options": {"cwd": "${workspaceFolder}/API"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}]}