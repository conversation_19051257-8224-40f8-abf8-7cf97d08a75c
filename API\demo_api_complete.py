#!/usr/bin/env python3
"""
Script de démonstration complète de l'API Notiflair

Ce script teste tous les endpoints principaux de l'API :
- Authentification admin
- Endpoints de notifications
- Endpoints de référence
"""

import json
import requests
from typing import Dict, Any


def pretty_print_json(data: Dict[str, Any], title: str = ""):
    """Affiche du JSON de manière formatée."""
    if title:
        print(f"\n{'='*60}")
        print(f"  {title}")
        print(f"{'='*60}")
    
    print(json.dumps(data, indent=2, ensure_ascii=False))


def test_endpoint(url: str, description: str, headers: Dict[str, str] = None):
    """Teste un endpoint et affiche le résultat."""
    try:
        print(f"\n🔍 Test: {description}")
        print(f"📡 URL: {url}")
        
        response = requests.get(url, headers=headers or {})
        
        if response.status_code == 200:
            print(f"✅ Statut: {response.status_code} OK")
            data = response.json()
            
            # Affichage limité pour les grandes réponses
            if isinstance(data, dict) and len(str(data)) > 1000:
                # Afficher seulement les clés principales
                summary = {}
                for key, value in data.items():
                    if isinstance(value, list) and len(value) > 3:
                        summary[key] = f"[{len(value)} éléments]"
                    elif isinstance(value, dict):
                        summary[key] = f"{{...}}"
                    else:
                        summary[key] = value
                pretty_print_json(summary, f"Résumé - {description}")
            else:
                pretty_print_json(data, description)
        else:
            print(f"❌ Erreur: {response.status_code}")
            print(f"Message: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Impossible de se connecter à {url}")
        print("💡 Assurez-vous que l'API est démarrée avec: python main.py")
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")


def test_auth_endpoint(url: str, data: Dict[str, Any], description: str):
    """Teste un endpoint d'authentification avec POST."""
    try:
        print(f"\n🔍 Test: {description}")
        print(f"📡 URL: {url}")
        
        response = requests.post(url, json=data)
        
        if response.status_code == 200:
            print(f"✅ Statut: {response.status_code} OK")
            response_data = response.json()
            pretty_print_json(response_data, description)
            return response_data
        else:
            print(f"❌ Erreur: {response.status_code}")
            print(f"Message: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Impossible de se connecter à {url}")
        return None
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return None


def main():
    """Fonction principale de démonstration."""
    base_url = "http://localhost:8000/api/v1"
    
    print("🔔 Démonstration complète de l'API Notiflair")
    print("=" * 60)
    
    # 1. Test d'authentification
    print("\n🔐 PHASE 1: Authentification Admin")
    login_data = {
        "username": "admin",
        "password": "secret"
    }
    
    auth_response = test_auth_endpoint(
        f"{base_url}/auth/login",
        login_data,
        "Connexion admin"
    )
    
    if not auth_response:
        print("❌ Impossible de continuer sans authentification")
        return
    
    # Récupérer le token
    token = auth_response.get("access_token")
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test des endpoints protégés
    test_endpoint(
        f"{base_url}/auth/me",
        "Informations utilisateur connecté",
        headers
    )
    
    test_endpoint(
        f"{base_url}/auth/users",
        "Liste des utilisateurs (superuser)",
        headers
    )
    
    # 2. Test des endpoints de référence
    print("\n📚 PHASE 2: Endpoints de Référence")
    
    test_endpoint(
        f"{base_url}/reference/countries",
        "Liste des pays"
    )
    
    test_endpoint(
        f"{base_url}/reference/languages",
        "Liste des langues"
    )
    
    test_endpoint(
        f"{base_url}/reference/notification-types",
        "Types de notifications"
    )
    
    # 3. Test des endpoints de notifications
    print("\n🔔 PHASE 3: Endpoints de Notifications")
    
    test_endpoint(
        f"{base_url}/notifications/hierarchy?country_code=BE&language_code=fr",
        "Hiérarchie des notifications (Belgique, français)"
    )
    
    test_endpoint(
        f"{base_url}/notifications/keywords?country_code=BE&language_code=fr",
        "Keywords des notifications (Belgique, français)"
    )
    
    # Test avec un autre pays
    test_endpoint(
        f"{base_url}/notifications/hierarchy?country_code=FR&language_code=fr",
        "Hiérarchie des notifications (France, français)"
    )
    
    # 4. Test d'erreurs
    print("\n⚠️ PHASE 4: Tests d'Erreurs")
    
    test_endpoint(
        f"{base_url}/notifications/hierarchy?country_code=XX&language_code=fr",
        "Test avec pays inexistant (devrait retourner 404)"
    )
    
    test_endpoint(
        f"{base_url}/auth/me",
        "Test sans token (devrait retourner 403)"
    )
    
    # 5. Résumé
    print(f"\n{'='*60}")
    print("✅ Démonstration terminée!")
    print("\n📊 Résumé des fonctionnalités testées:")
    print("   🔐 Authentification admin avec JWT")
    print("   👥 Gestion des utilisateurs et rôles")
    print("   🌍 Données de référence (pays, langues, types)")
    print("   🔔 Hiérarchie complète des notifications")
    print("   🏷️ Keywords avec traductions")
    print("   ⚠️ Gestion d'erreurs et sécurité")
    
    print("\n💡 Pour explorer l'API interactivement:")
    print("   👉 Ouvrez http://localhost:8000/docs dans votre navigateur")
    
    print("\n📖 Documentation complète:")
    print("   📄 README.md - Guide de démarrage")
    print("   📄 docs/endpoints-notifications.md - Documentation des endpoints")
    print("   📄 docs/database.md - Structure de la base de données")
    
    print(f"\n{'='*60}")


if __name__ == "__main__":
    main()
