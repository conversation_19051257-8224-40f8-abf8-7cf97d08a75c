"""
Router pour les données de référence

Endpoints pour consulter les pays, langues et autres données statiques.
"""

import logging
from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session, selectinload
from sqlalchemy import func

from app.database import get_db
from app.models.reference import Country, Language, CountryLanguage
from app.schemas.reference import (
    CountryResponse,
    LanguageResponse,
    CountryWithLanguagesResponse,
    CountryLanguageResponse,
    CountriesListResponse
)

# Configuration du logger
logger = logging.getLogger(__name__)

# Création du router
router = APIRouter(prefix="/reference", tags=["Reference Data"])


@router.get(
    "/countries",
    response_model=CountriesListResponse,
    summary="Liste tous les pays avec leurs langues",
    description="""
    Retourne la liste complète de tous les pays disponibles avec :
    - Toutes les données du pays (nom, code, timezone, dates, etc.)
    - La langue par défaut du pays
    - Toutes les langues disponibles dans ce pays
    
    Cet endpoint fournit toutes les données nécessaires pour configurer
    l'application mobile selon le pays de l'utilisateur.
    """
)
async def get_all_countries(
    db: Session = Depends(get_db),
    include_inactive: bool = Query(False, description="Inclure les pays inactifs")
) -> CountriesListResponse:
    """
    Récupère tous les pays avec leurs langues associées.
    
    Args:
        db: Session de base de données
        include_inactive: Inclure les pays inactifs (pour usage futur)
        
    Returns:
        CountriesListResponse: Liste complète des pays avec métadonnées
        
    Raises:
        HTTPException: En cas d'erreur de base de données
    """
    try:
        logger.info("Récupération de tous les pays avec leurs langues")
        
        # Requête optimisée avec eager loading pour éviter le problème N+1
        countries_query = db.query(Country).options(
            selectinload(Country.default_language),
            selectinload(Country.languages)
        ).order_by(Country.cty_name)
        
        countries = countries_query.all()
        
        # Calcul des statistiques
        total_countries = len(countries)
        
        # Compter les langues uniques
        unique_languages = db.query(Language.lng_id).distinct().count()
        
        logger.info(f"Trouvé {total_countries} pays et {unique_languages} langues")
        
        return CountriesListResponse(
            countries=countries,
            total_countries=total_countries,
            total_languages=unique_languages
        )
        
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des pays: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Erreur lors de la récupération des données des pays"
        )


@router.get(
    "/countries/{country_code}",
    response_model=CountryWithLanguagesResponse,
    summary="Détails d'un pays spécifique",
    description="""
    Retourne les détails complets d'un pays spécifique identifié par son code ISO.
    
    Inclut toutes les données du pays et ses langues disponibles.
    """
)
async def get_country_by_code(
    country_code: str,
    db: Session = Depends(get_db)
) -> CountryWithLanguagesResponse:
    """
    Récupère un pays spécifique par son code ISO.
    
    Args:
        country_code: Code ISO 3166-1 alpha-2 du pays (ex: "FR", "BE")
        db: Session de base de données
        
    Returns:
        CountryWithLanguagesResponse: Données complètes du pays
        
    Raises:
        HTTPException: Si le pays n'est pas trouvé ou erreur de base de données
    """
    try:
        logger.info(f"Récupération du pays avec le code: {country_code}")
        
        # Normaliser le code pays en majuscules
        country_code = country_code.upper()
        
        # Requête avec eager loading
        country = db.query(Country).options(
            selectinload(Country.default_language),
            selectinload(Country.languages)
        ).filter(Country.cty_code == country_code).first()
        
        if not country:
            logger.warning(f"Pays non trouvé avec le code: {country_code}")
            raise HTTPException(
                status_code=404,
                detail=f"Pays non trouvé avec le code: {country_code}"
            )
        
        logger.info(f"Pays trouvé: {country.cty_name} ({country.cty_code})")
        
        return country
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erreur lors de la récupération du pays {country_code}: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Erreur lors de la récupération des données du pays"
        )


@router.get(
    "/languages",
    response_model=List[LanguageResponse],
    summary="Liste toutes les langues disponibles",
    description="""
    Retourne la liste de toutes les langues disponibles dans l'application.
    
    Utile pour les interfaces d'administration ou de configuration.
    """
)
async def get_all_languages(
    db: Session = Depends(get_db)
) -> List[LanguageResponse]:
    """
    Récupère toutes les langues disponibles.
    
    Args:
        db: Session de base de données
        
    Returns:
        List[LanguageResponse]: Liste de toutes les langues
        
    Raises:
        HTTPException: En cas d'erreur de base de données
    """
    try:
        logger.info("Récupération de toutes les langues")

        languages = db.query(Language).order_by(Language.lng_name).all()

        logger.info(f"Trouvé {len(languages)} langues")

        return languages

    except Exception as e:
        logger.error(f"Erreur lors de la récupération des langues: {e}")
        raise HTTPException(
            status_code=500,
            detail="Erreur lors de la récupération des langues"
        )


@router.get(
    "/notification-types",
    response_model=List[Dict[str, Any]],
    summary="Liste des types de notifications",
    description="Récupère la liste de tous les types de notifications avec leur hiérarchie"
)
async def get_notification_types(
    db: Session = Depends(get_db)
) -> List[Dict[str, Any]]:
    """
    Récupère tous les types de notifications.

    Retourne la liste complète des types de notifications avec :
    - Identifiants et clés techniques
    - Hiérarchie (parent/enfant)
    - Métadonnées (couleurs, icônes, ordre)
    """
    try:
        # Requête pour récupérer tous les types de notifications
        types_query = db.query(NotificationType).order_by(
            NotificationType.ntp_display_order.asc(),
            NotificationType.ntp_identifier_key.asc()
        )

        notification_types = types_query.all()

        # Convertir en dictionnaire
        types_list = []
        for ntype in notification_types:
            types_list.append({
                "ntp_id": ntype.ntp_id,
                "ntp_identifier_key": ntype.ntp_identifier_key,
                "ntp_icon": ntype.ntp_icon,
                "ntp_color": ntype.ntp_color,
                "ntp_bg_color": ntype.ntp_bg_color,
                "ntp_display_order": ntype.ntp_display_order,
                "ntp_parent_id": ntype.ntp_parent_id,
                "ntp_level": ntype.ntp_level,
                "ntp_description_template": ntype.ntp_description_template,
                "ntp_default_enabled": ntype.ntp_default_enabled
            })

        return types_list

    except Exception as e:
        logger.error(f"Erreur lors de la récupération des types de notifications: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la récupération des types de notifications"
        )


@router.get(
    "/stats",
    summary="Statistiques des données de référence",
    description="""
    Retourne des statistiques sur les données de référence :
    - Nombre de pays
    - Nombre de langues
    - Répartition des langues par pays
    """
)
async def get_reference_stats(
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Récupère les statistiques des données de référence.
    
    Args:
        db: Session de base de données
        
    Returns:
        Dict[str, Any]: Statistiques détaillées
        
    Raises:
        HTTPException: En cas d'erreur de base de données
    """
    try:
        logger.info("Calcul des statistiques de référence")
        
        # Compter les pays
        total_countries = db.query(Country).count()
        
        # Compter les langues
        total_languages = db.query(Language).count()
        
        # Compter les associations pays-langues
        from sqlalchemy import select
        total_associations = db.execute(
            select(func.count()).select_from(country_language_association)
        ).scalar()
        
        # Moyenne de langues par pays
        avg_languages_per_country = total_associations / total_countries if total_countries > 0 else 0
        
        # Pays avec le plus de langues
        country_with_most_languages = db.query(
            Country.cty_name,
            Country.cty_code,
            func.count(country_language_association.c.lng_id).label('language_count')
        ).join(country_language_association).group_by(
            Country.cty_id, Country.cty_name, Country.cty_code
        ).order_by(func.count(country_language_association.c.lng_id).desc()).first()
        
        stats = {
            "total_countries": total_countries,
            "total_languages": total_languages,
            "total_country_language_associations": total_associations,
            "average_languages_per_country": round(avg_languages_per_country, 2),
            "country_with_most_languages": {
                "name": country_with_most_languages.cty_name if country_with_most_languages else None,
                "code": country_with_most_languages.cty_code if country_with_most_languages else None,
                "language_count": country_with_most_languages.language_count if country_with_most_languages else 0
            } if country_with_most_languages else None
        }
        
        logger.info(f"Statistiques calculées: {stats}")
        
        return stats
        
    except Exception as e:
        logger.error(f"Erreur lors du calcul des statistiques: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Erreur lors du calcul des statistiques"
        )
