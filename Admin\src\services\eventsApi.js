import apiService from './api.js';

class EventsApiService {
  /**
   * Récupère la hiérarchie complète des notifications/événements
   * @param {string} countryCode - Code pays (ex: "FR", "BE")
   * @param {string} languageCode - Code langue (ex: "fr", "en")
   * @param {boolean} includeInactive - Inclure les événements inactifs
   * @returns {Promise<Object>} Hiérarchie des événements
   */
  async getHierarchy(countryCode = 'FR', languageCode = 'fr', includeInactive = false) {
    try {
      const params = new URLSearchParams({
        country_code: countryCode,
        language_code: languageCode,
        include_inactive: includeInactive.toString()
      });

      const response = await apiService.get(`/notifications/hierarchy?${params}`);
      return response;
    } catch (error) {
      console.error('❌ Erreur lors de la récupération de la hiérarchie:', error);
      throw error;
    }
  }

  /**
   * Récupère les types de notifications (sans traductions)
   * @returns {Promise<Array>} Liste des types de notifications
   */
  async getNotificationTypes() {
    try {
      const response = await apiService.get('/reference/notification-types');
      return response;
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des types:', error);
      throw error;
    }
  }

  /**
   * Récupère les keywords d'une notification
   * @param {string} countryCode - Code pays
   * @param {string} languageCode - Code langue
   * @param {number} notificationId - ID de la notification (optionnel)
   * @returns {Promise<Object>} Keywords de la notification
   */
  async getKeywords(countryCode = 'FR', languageCode = 'fr', notificationId = null) {
    try {
      const params = new URLSearchParams({
        country_code: countryCode,
        language_code: languageCode
      });

      if (notificationId) {
        params.append('notification_id', notificationId.toString());
      }

      const response = await apiService.get(`/notifications/keywords?${params}`);
      return response;
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des keywords:', error);
      throw error;
    }
  }

  /**
   * Récupère la liste des pays disponibles
   * @returns {Promise<Array>} Liste des pays
   */
  async getCountries() {
    try {
      const response = await apiService.get('/reference/countries');
      return response;
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des pays:', error);
      throw error;
    }
  }

  /**
   * Récupère la liste des langues disponibles
   * @returns {Promise<Array>} Liste des langues
   */
  async getLanguages() {
    try {
      const response = await apiService.get('/reference/languages');
      return response;
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des langues:', error);
      throw error;
    }
  }

  /**
   * Transforme la hiérarchie API en format utilisable par le composant
   * @param {Object} hierarchyData - Données de l'API
   * @returns {Object} Données transformées
   */
  transformHierarchyData(hierarchyData) {
    if (!hierarchyData || !hierarchyData.hierarchy) {
      return { types: [], stats: {} };
    }

    const stats = {
      totalTypes: hierarchyData.total_types || 0,
      totalNotifications: hierarchyData.total_notifications || 0,
      activeNotifications: hierarchyData.active_notifications || 0,
      countryCode: hierarchyData.country_code,
      languageCode: hierarchyData.language_code
    };

    const types = hierarchyData.hierarchy.map(item => this.transformHierarchyItem(item));

    return { types, stats };
  }

  /**
   * Transforme un élément de hiérarchie récursivement
   * @param {Object} item - Élément de hiérarchie de l'API
   * @returns {Object} Élément transformé
   */
  transformHierarchyItem(item) {
    const type = item.notification_type;
    const notifications = item.notifications || [];
    const children = (item.children || []).map(child => this.transformHierarchyItem(child));

    return {
      // Informations du type
      id: type.ntp_id,
      key: type.ntp_identifier_key,
      label: type.label || type.ntp_identifier_key,
      description: type.description,
      icon: type.ntp_icon,
      color: type.ntp_color,
      bgColor: type.ntp_bg_color,
      level: type.ntp_level,
      parentId: type.ntp_parent_id,
      displayOrder: type.ntp_display_order,
      defaultEnabled: type.ntp_default_enabled,
      translationSource: type.translation_source,

      // Notifications de ce type
      notifications: notifications.map(notif => ({
        id: notif.ntf_id,
        key: notif.ntf_identifier_key,
        label: notif.label || notif.ntf_identifier_key,
        description: notif.description,
        isActive: notif.ntf_is_active,
        icon: notif.ntf_icon,
        usageCount: notif.ntf_usage_count || 0,
        relevanceScore: notif.ntf_relevance_score,
        countryCode: notif.country_code,
        countryName: notif.country_name,
        translationSource: notif.translation_source,
        rrule: notif.ntf_rrule,
        rruleText: notif.ntf_rrule_text
      })),

      // Sous-types
      children: children,

      // Statistiques calculées
      totalNotifications: notifications.length + children.reduce((sum, child) => sum + child.totalNotifications, 0)
    };
  }
}

const eventsApiService = new EventsApiService();
export default eventsApiService;
