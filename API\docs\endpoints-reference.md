# 📍 Endpoints de Référence - Documentation

## Vue d'ensemble

Les endpoints de référence permettent de consulter les données statiques de l'application Notiflair, notamment les pays supportés et leurs langues associées.

## Endpoints disponibles

### 🌍 GET `/api/v1/reference/countries`

Retourne la liste complète de tous les pays disponibles avec leurs langues associées.

**Réponse :**
```json
{
  "countries": [
    {
      "cty_id": 1,
      "cty_code": "BE",
      "cty_name": "Belgique",
      "cty_timezone": "Europe/Brussels",
      "cty_default_lng_id": 1,
      "cty_created_at": "2025-06-23T15:18:13",
      "cty_updated_at": "2025-06-23T15:18:13",
      "default_language": {
        "lng_id": 1,
        "lng_code": "fr",
        "lng_name": "Français"
      },
      "languages": [
        {
          "lng_id": 1,
          "lng_code": "fr",
          "lng_name": "Français"
        },
        {
          "lng_id": 2,
          "lng_code": "nl",
          "lng_name": "Nederlands"
        }
      ]
    }
  ],
  "total_countries": 4,
  "total_languages": 5
}
```

**Paramètres de requête :**
- `include_inactive` (bool, optionnel) : Inclure les pays inactifs (défaut: false)

### 🏴 GET `/api/v1/reference/countries/{country_code}`

Retourne les détails d'un pays spécifique identifié par son code ISO.

**Paramètres :**
- `country_code` (string) : Code ISO 3166-1 alpha-2 du pays (ex: "FR", "BE")

**Exemple :** `GET /api/v1/reference/countries/BE`

**Réponse :** Même structure qu'un élément du tableau `countries` ci-dessus.

**Codes d'erreur :**
- `404` : Pays non trouvé
- `500` : Erreur serveur

### 🗣️ GET `/api/v1/reference/languages`

Retourne la liste de toutes les langues disponibles dans l'application.

**Réponse :**
```json
[
  {
    "lng_id": 1,
    "lng_code": "fr",
    "lng_name": "Français"
  },
  {
    "lng_id": 2,
    "lng_code": "en",
    "lng_name": "English"
  }
]
```

### 📊 GET `/api/v1/reference/stats`

Retourne des statistiques sur les données de référence.

**Réponse :**
```json
{
  "total_countries": 4,
  "total_languages": 5,
  "total_country_language_associations": 8,
  "average_languages_per_country": 2.0,
  "country_with_most_languages": {
    "name": "Belgique",
    "code": "BE",
    "language_count": 3
  }
}
```

## Structure des données

### Pays (Country)

| Champ | Type | Description |
|-------|------|-------------|
| `cty_id` | integer | Identifiant unique du pays |
| `cty_code` | string | Code ISO 3166-1 alpha-2 (FR, BE...) |
| `cty_name` | string | Nom officiel du pays |
| `cty_timezone` | string | Fuseau horaire IANA (Europe/Paris...) |
| `cty_default_lng_id` | integer | ID de la langue par défaut |
| `cty_created_at` | datetime | Date de création |
| `cty_updated_at` | datetime | Dernière modification |
| `default_language` | Language | Langue par défaut du pays |
| `languages` | Language[] | Toutes les langues disponibles |

### Langue (Language)

| Champ | Type | Description |
|-------|------|-------------|
| `lng_id` | integer | Identifiant unique de la langue |
| `lng_code` | string | Code ISO 639-1 (fr, en, nl...) |
| `lng_name` | string | Nom de la langue dans sa propre langue |

## Exemples d'utilisation

### Curl

```bash
# Récupérer tous les pays
curl -X GET "http://localhost:8000/api/v1/reference/countries" \
     -H "accept: application/json"

# Récupérer un pays spécifique
curl -X GET "http://localhost:8000/api/v1/reference/countries/BE" \
     -H "accept: application/json"

# Récupérer toutes les langues
curl -X GET "http://localhost:8000/api/v1/reference/languages" \
     -H "accept: application/json"

# Récupérer les statistiques
curl -X GET "http://localhost:8000/api/v1/reference/stats" \
     -H "accept: application/json"
```

### Python

```python
import requests

# Configuration
base_url = "http://localhost:8000/api/v1/reference"

# Récupérer tous les pays
response = requests.get(f"{base_url}/countries")
countries_data = response.json()

print(f"Nombre de pays: {countries_data['total_countries']}")
for country in countries_data['countries']:
    print(f"- {country['cty_name']} ({country['cty_code']})")
    print(f"  Langues: {[lang['lng_name'] for lang in country['languages']]}")
```

### JavaScript

```javascript
// Récupérer tous les pays
fetch('http://localhost:8000/api/v1/reference/countries')
  .then(response => response.json())
  .then(data => {
    console.log(`Nombre de pays: ${data.total_countries}`);
    data.countries.forEach(country => {
      console.log(`${country.cty_name} (${country.cty_code})`);
      console.log(`Langues: ${country.languages.map(l => l.lng_name).join(', ')}`);
    });
  });
```

## Tests

Pour exécuter les tests des endpoints :

```bash
# Tests spécifiques aux endpoints de référence
python -m pytest tests/test_reference.py -v

# Tous les tests
python -m pytest -v
```

## Démonstration

Un script de démonstration est disponible :

```bash
# Démarrer l'API
python main.py

# Dans un autre terminal, exécuter la démonstration
python demo_endpoints.py
```

## Documentation interactive

L'API fournit une documentation interactive Swagger accessible à :
- **Swagger UI** : http://localhost:8000/docs
- **ReDoc** : http://localhost:8000/redoc

## Notes techniques

- **Performance** : Les requêtes utilisent l'eager loading pour éviter le problème N+1
- **Cache** : SQLAlchemy met en cache les requêtes répétées
- **Logging** : Toutes les opérations sont loggées pour le monitoring
- **Validation** : Les données sont validées avec Pydantic
- **Erreurs** : Gestion d'erreurs complète avec codes HTTP appropriés
