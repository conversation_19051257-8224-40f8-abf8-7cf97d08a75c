"""
Tests pour la synchronisation Notiflair API

Tests des endpoints de synchronisation et snapshots.
"""

import pytest
from fastapi.testclient import TestClient


class TestSynchronization:
    """Tests de synchronisation."""
    
    def test_check_version(self, client: TestClient, auth_headers):
        """Test de vérification de version."""
        response = client.get(
            "/api/v1/sync/version?country_code=FR&language_code=fr", 
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Vérification de la structure de réponse
        assert "server_version" in data
        assert "client_version" in data
        assert "update_available" in data
        assert "update_required" in data
        assert "snapshot_available" in data
        
        assert isinstance(data["update_available"], bool)
        assert isinstance(data["update_required"], bool)
        assert isinstance(data["snapshot_available"], bool)
    
    def test_check_version_with_client_version(self, client: TestClient, auth_headers):
        """Test de vérification avec version client."""
        response = client.get(
            "/api/v1/sync/version?country_code=FR&language_code=fr&current_version=v1.0.0", 
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["client_version"] == "v1.0.0"
        assert "server_version" in data
    
    def test_check_version_invalid_country(self, client: TestClient, auth_headers):
        """Test avec code pays invalide."""
        response = client.get(
            "/api/v1/sync/version?country_code=INVALID&language_code=fr", 
            headers=auth_headers
        )
        
        # Doit retourner une erreur
        assert response.status_code == 500
    
    def test_check_version_missing_params(self, client: TestClient, auth_headers):
        """Test avec paramètres manquants."""
        # Sans country_code
        response1 = client.get(
            "/api/v1/sync/version?language_code=fr", 
            headers=auth_headers
        )
        assert response1.status_code == 422
        
        # Sans language_code
        response2 = client.get(
            "/api/v1/sync/version?country_code=FR", 
            headers=auth_headers
        )
        assert response2.status_code == 422
    
    def test_get_snapshot(self, client: TestClient, auth_headers):
        """Test de récupération de snapshot."""
        response = client.get(
            "/api/v1/sync/snapshot?country_code=FR&language_code=fr", 
            headers=auth_headers
        )
        
        # Peut être 404 si aucun snapshot n'existe
        assert response.status_code in [200, 404]
        
        if response.status_code == 200:
            data = response.json()
            
            # Vérification de la structure
            assert "version" in data
            assert "country_code" in data
            assert "language_code" in data
            assert "generated_at" in data
            assert "checksum" in data
            assert "size_bytes" in data
            assert "size_compressed" in data
            assert "item_count" in data
            
            assert data["country_code"] == "FR"
            assert data["language_code"] == "fr"
    
    def test_get_snapshot_with_version(self, client: TestClient, auth_headers):
        """Test de récupération avec version spécifique."""
        response = client.get(
            "/api/v1/sync/snapshot?country_code=FR&language_code=fr&version=v1.0.0", 
            headers=auth_headers
        )
        
        # Peut être 404 si la version n'existe pas
        assert response.status_code in [200, 404]
    
    def test_get_snapshot_uncompressed(self, client: TestClient, auth_headers):
        """Test de récupération non compressée."""
        response = client.get(
            "/api/v1/sync/snapshot?country_code=FR&language_code=fr&compressed=false", 
            headers=auth_headers
        )
        
        assert response.status_code in [200, 404]
        
        if response.status_code == 200:
            data = response.json()
            # Doit contenir les données décompressées
            assert "data" in data
    
    def test_download_snapshot_binary(self, client: TestClient, auth_headers):
        """Test de téléchargement binaire."""
        response = client.get(
            "/api/v1/sync/snapshot/download?country_code=FR&language_code=fr", 
            headers=auth_headers
        )
        
        # Peut être 404 si aucun snapshot n'existe
        assert response.status_code in [200, 404]
        
        if response.status_code == 200:
            # Vérification des headers
            assert "Content-Disposition" in response.headers
            assert "Content-Length" in response.headers
            assert "X-Snapshot-Version" in response.headers
            assert "X-Snapshot-Checksum" in response.headers
            
            # Content-Type doit être application/gzip
            assert response.headers["Content-Type"] == "application/gzip"
    
    def test_get_sync_status(self, client: TestClient, auth_headers):
        """Test de récupération du statut de sync."""
        response = client.get("/api/v1/sync/status", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        
        # Vérification de la structure
        assert "user_id" in data
        assert "device_id" in data
        assert "last_sync" in data
        assert "sync_count" in data
        assert "available_versions" in data
        assert "pending_updates" in data
        
        assert isinstance(data["user_id"], int)
        assert isinstance(data["device_id"], str)
        assert isinstance(data["sync_count"], int)
        assert isinstance(data["available_versions"], list)
        assert isinstance(data["pending_updates"], list)
    
    def test_get_available_countries(self, client: TestClient, auth_headers):
        """Test de récupération des pays disponibles."""
        response = client.get("/api/v1/sync/countries", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "countries" in data
        assert isinstance(data["countries"], list)
        
        # Vérification de la structure des pays si ils existent
        if data["countries"]:
            country = data["countries"][0]
            assert "cty_id" in country
            assert "cty_code" in country
            assert "cty_name" in country
            assert "cty_timezone" in country
    
    def test_get_available_languages(self, client: TestClient, auth_headers):
        """Test de récupération des langues disponibles."""
        response = client.get("/api/v1/sync/languages", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "languages" in data
        assert isinstance(data["languages"], list)
        
        # Vérification de la structure des langues si elles existent
        if data["languages"]:
            language = data["languages"][0]
            assert "lng_id" in language
            assert "lng_code" in language
            assert "lng_name" in language
    
    def test_get_languages_filtered_by_country(self, client: TestClient, auth_headers):
        """Test de récupération des langues filtrées par pays."""
        response = client.get(
            "/api/v1/sync/languages?country_code=FR", 
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "languages" in data
    
    def test_sync_endpoints_without_auth(self, client: TestClient):
        """Test d'accès aux endpoints de sync sans authentification."""
        endpoints = [
            "/api/v1/sync/version?country_code=FR&language_code=fr",
            "/api/v1/sync/snapshot?country_code=FR&language_code=fr",
            "/api/v1/sync/status",
            "/api/v1/sync/countries",
            "/api/v1/sync/languages"
        ]
        
        for endpoint in endpoints:
            response = client.get(endpoint)
            assert response.status_code == 403  # Forbidden


class TestSyncEdgeCases:
    """Tests de cas limites pour la synchronisation."""
    
    def test_version_check_case_sensitivity(self, client: TestClient, auth_headers):
        """Test de sensibilité à la casse des codes."""
        # Test avec différentes casses
        test_cases = [
            ("fr", "FR"),
            ("FR", "fr"),
            ("fr", "fr"),
            ("FR", "FR")
        ]
        
        for country, language in test_cases:
            response = client.get(
                f"/api/v1/sync/version?country_code={country}&language_code={language}", 
                headers=auth_headers
            )
            
            # Doit normaliser automatiquement
            assert response.status_code in [200, 500]  # 500 si pays/langue non supporté
    
    def test_snapshot_with_invalid_version_format(self, client: TestClient, auth_headers):
        """Test avec format de version invalide."""
        invalid_versions = [
            "1.0.0",      # Sans 'v'
            "v1.0",       # Incomplet
            "invalid",    # Non numérique
            "v1.0.0.0"    # Trop de parties
        ]
        
        for version in invalid_versions:
            response = client.get(
                f"/api/v1/sync/snapshot?country_code=FR&language_code=fr&version={version}", 
                headers=auth_headers
            )
            
            # Peut être 404 (version non trouvée) ou 400 (format invalide)
            assert response.status_code in [200, 400, 404]
    
    def test_concurrent_snapshot_downloads(self, client: TestClient, auth_headers):
        """Test de téléchargements simultanés."""
        # Simulation de téléchargements multiples
        responses = []
        
        for i in range(3):
            response = client.get(
                "/api/v1/sync/snapshot?country_code=FR&language_code=fr", 
                headers=auth_headers
            )
            responses.append(response)
        
        # Tous doivent avoir le même statut
        status_codes = [r.status_code for r in responses]
        assert len(set(status_codes)) == 1  # Tous identiques
    
    def test_snapshot_download_with_special_characters(self, client: TestClient, auth_headers):
        """Test avec caractères spéciaux dans les paramètres."""
        # Test avec caractères qui pourraient causer des problèmes
        special_cases = [
            ("F%20R", "fr"),  # URL encoded
            ("FR", "f%20r"),
            ("FR@", "fr"),    # Caractères spéciaux
            ("FR", "fr@")
        ]
        
        for country, language in special_cases:
            response = client.get(
                f"/api/v1/sync/version?country_code={country}&language_code={language}", 
                headers=auth_headers
            )
            
            # Doit gérer gracieusement les caractères spéciaux
            assert response.status_code in [200, 422, 500]


class TestSyncPerformance:
    """Tests de performance pour la synchronisation."""
    
    def test_version_check_response_time(self, client: TestClient, auth_headers):
        """Test du temps de réponse de vérification de version."""
        import time
        
        start_time = time.time()
        response = client.get(
            "/api/v1/sync/version?country_code=FR&language_code=fr", 
            headers=auth_headers
        )
        end_time = time.time()
        
        response_time = end_time - start_time
        
        # La vérification de version doit être rapide (< 2 secondes)
        assert response_time < 2.0
        assert response.status_code in [200, 500]
    
    def test_multiple_version_checks(self, client: TestClient, auth_headers):
        """Test de vérifications multiples rapides."""
        # Plusieurs vérifications rapides
        for i in range(5):
            response = client.get(
                "/api/v1/sync/version?country_code=FR&language_code=fr", 
                headers=auth_headers
            )
            
            # Toutes doivent réussir ou échouer de manière cohérente
            assert response.status_code in [200, 500]
    
    def test_snapshot_size_headers(self, client: TestClient, auth_headers):
        """Test des headers de taille de snapshot."""
        response = client.get(
            "/api/v1/sync/snapshot?country_code=FR&language_code=fr", 
            headers=auth_headers
        )
        
        if response.status_code == 200:
            data = response.json()
            
            # Vérification que les tailles sont cohérentes
            assert data["size_bytes"] >= 0
            assert data["size_compressed"] >= 0
            assert data["item_count"] >= 0
            
            # La taille compressée doit être <= taille originale
            assert data["size_compressed"] <= data["size_bytes"]
