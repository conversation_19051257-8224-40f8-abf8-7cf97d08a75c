{"version": "0.2.0", "configurations": [{"name": "FastAPI Debug", "type": "python", "request": "launch", "program": "${workspaceFolder}/API/main.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/API", "env": {"PYTHONPATH": "${workspaceFolder}/API"}, "args": []}, {"name": "Python: Current File", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "cwd": "${fileDirname}"}, {"name": "Python: Tests", "type": "python", "request": "launch", "module": "pytest", "args": ["${workspaceFolder}/API/tests", "-v"], "console": "integratedTerminal", "cwd": "${workspaceFolder}/API"}]}