"""
Tests pour la gestion des utilisateurs Notiflair API

Tests des endpoints utilisateurs et de leurs données.
"""

import pytest
from fastapi.testclient import TestClient
from datetime import datetime


class TestUsers:
    """Tests de gestion des utilisateurs."""
    
    def test_get_current_user_info(self, client: TestClient, auth_headers):
        """Test de récupération des informations utilisateur."""
        response = client.get("/api/v1/users/me", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        
        # Vérification de la structure de réponse
        assert "usr_id" in data
        assert "usr_device_id" in data
        assert "email" in data
        assert "usr_created_at" in data
        assert "usr_last_sync" in data
        
        assert isinstance(data["usr_id"], int)
        assert isinstance(data["usr_device_id"], str)
        assert len(data["usr_device_id"]) >= 10
    
    def test_get_current_user_without_auth(self, client: TestClient):
        """Test de récupération sans authentification."""
        response = client.get("/api/v1/users/me")
        assert response.status_code == 403  # Forbidden
    
    def test_update_current_user_email(self, client: TestClient, auth_headers):
        """Test de mise à jour de l'email utilisateur."""
        update_data = {
            "email": "<EMAIL>"
        }
        
        response = client.put("/api/v1/users/me", json=update_data, headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["email"] == "<EMAIL>"
    
    def test_update_current_user_invalid_email(self, client: TestClient, auth_headers):
        """Test de mise à jour avec email invalide."""
        update_data = {
            "email": "invalid-email"  # Pas de @
        }
        
        response = client.put("/api/v1/users/me", json=update_data, headers=auth_headers)
        assert response.status_code == 422  # Validation error
    
    def test_update_current_user_empty_data(self, client: TestClient, auth_headers):
        """Test de mise à jour avec données vides."""
        update_data = {}
        
        response = client.put("/api/v1/users/me", json=update_data, headers=auth_headers)
        
        # Doit réussir même avec des données vides
        assert response.status_code == 200
    
    def test_update_sync_timestamp(self, client: TestClient, auth_headers):
        """Test de mise à jour du timestamp de synchronisation."""
        # Récupération de l'état initial
        initial_response = client.get("/api/v1/users/me", headers=auth_headers)
        initial_data = initial_response.json()
        initial_sync = initial_data["usr_last_sync"]
        
        # Mise à jour du timestamp
        response = client.post("/api/v1/users/sync", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        
        # Vérification que le timestamp a été mis à jour
        updated_response = client.get("/api/v1/users/me", headers=auth_headers)
        updated_data = updated_response.json()
        updated_sync = updated_data["usr_last_sync"]
        
        # Le nouveau timestamp doit être différent (plus récent)
        if initial_sync:
            assert updated_sync != initial_sync
        else:
            assert updated_sync is not None
    
    def test_delete_current_user(self, client: TestClient, auth_headers):
        """Test de suppression du compte utilisateur."""
        response = client.delete("/api/v1/users/me", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        
        # Vérification que l'utilisateur n'existe plus
        # Tentative d'accès aux infos utilisateur
        get_response = client.get("/api/v1/users/me", headers=auth_headers)
        assert get_response.status_code == 401  # Token invalide car utilisateur supprimé
    
    def test_user_lifecycle_complete(self, client: TestClient):
        """Test du cycle de vie complet d'un utilisateur."""
        device_id = "test-lifecycle-device-12345"
        
        # 1. Création via login
        login_data = {
            "device_id": device_id,
            "email": "<EMAIL>"
        }
        
        login_response = client.post("/api/v1/auth/login", json=login_data)
        assert login_response.status_code == 200
        
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        
        # 2. Récupération des infos
        info_response = client.get("/api/v1/users/me", headers=headers)
        assert info_response.status_code == 200
        user_data = info_response.json()
        
        assert user_data["usr_device_id"] == device_id
        assert user_data["email"] == "<EMAIL>"
        
        # 3. Mise à jour
        update_response = client.put(
            "/api/v1/users/me", 
            json={"email": "<EMAIL>"}, 
            headers=headers
        )
        assert update_response.status_code == 200
        
        # 4. Synchronisation
        sync_response = client.post("/api/v1/users/sync", headers=headers)
        assert sync_response.status_code == 200
        
        # 5. Suppression
        delete_response = client.delete("/api/v1/users/me", headers=headers)
        assert delete_response.status_code == 200


class TestUserEdgeCases:
    """Tests de cas limites pour les utilisateurs."""
    
    def test_update_user_with_existing_email(self, client: TestClient):
        """Test de mise à jour avec un email déjà utilisé."""
        # Création de deux utilisateurs
        user1_data = {
            "device_id": "test-device-user1-12345",
            "email": "<EMAIL>"
        }
        user2_data = {
            "device_id": "test-device-user2-12345",
            "email": "<EMAIL>"
        }
        
        # Connexion des deux utilisateurs
        response1 = client.post("/api/v1/auth/login", json=user1_data)
        response2 = client.post("/api/v1/auth/login", json=user2_data)
        
        assert response1.status_code == 200
        assert response2.status_code == 200
        
        headers2 = {"Authorization": f"Bearer {response2.json()['access_token']}"}
        
        # Tentative de mise à jour de user2 avec l'email de user1
        update_data = {"email": "<EMAIL>"}
        
        # Note: Selon la logique métier, cela pourrait être autorisé ou non
        # Pour ce test, on suppose que c'est autorisé (pas de contrainte unique sur email)
        response = client.put("/api/v1/users/me", json=update_data, headers=headers2)
        
        # Le comportement dépend de l'implémentation
        # Si les emails dupliqués sont autorisés : 200
        # Si ils ne le sont pas : 400
        assert response.status_code in [200, 400]
    
    def test_multiple_sync_updates(self, client: TestClient, auth_headers):
        """Test de multiples mises à jour de synchronisation."""
        timestamps = []
        
        # Plusieurs mises à jour rapides
        for i in range(3):
            response = client.post("/api/v1/users/sync", headers=auth_headers)
            assert response.status_code == 200
            
            # Récupération du timestamp
            info_response = client.get("/api/v1/users/me", headers=auth_headers)
            user_data = info_response.json()
            timestamps.append(user_data["usr_last_sync"])
        
        # Tous les timestamps doivent être différents (ou au moins le dernier)
        assert len(set(timestamps)) >= 1
    
    def test_user_operations_with_expired_token(self, client: TestClient):
        """Test d'opérations avec un token expiré."""
        # Note: Ce test nécessiterait de manipuler l'expiration du token
        # ou d'attendre l'expiration, ce qui n'est pas pratique dans un test unitaire
        # On peut simuler avec un token invalide
        
        invalid_headers = {"Authorization": "Bearer expired_or_invalid_token"}
        
        # Toutes les opérations doivent échouer
        operations = [
            ("GET", "/api/v1/users/me"),
            ("PUT", "/api/v1/users/me"),
            ("POST", "/api/v1/users/sync"),
            ("DELETE", "/api/v1/users/me")
        ]
        
        for method, endpoint in operations:
            if method == "GET":
                response = client.get(endpoint, headers=invalid_headers)
            elif method == "PUT":
                response = client.put(endpoint, json={}, headers=invalid_headers)
            elif method == "POST":
                response = client.post(endpoint, headers=invalid_headers)
            elif method == "DELETE":
                response = client.delete(endpoint, headers=invalid_headers)
            
            assert response.status_code == 401
