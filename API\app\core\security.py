"""
Sécurité et authentification pour l'API Notiflair

Ce module contient les dépendances FastAPI pour l'authentification JWT
et la protection des routes admin.
"""

from datetime import datetime, timedelta
from typing import Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt

from app.config import settings
from app.services.auth import AuthService, get_auth_service, AdminUser
from app.schemas.auth import TokenData


# Configuration du schéma de sécurité Bearer
security = HTTPBearer(
    scheme_name="JWT",
    description="Token JWT d'authentification admin"
)


class JWTBearer(HTTPBearer):
    """Classe personnalisée pour la validation JWT."""
    
    def __init__(self, auto_error: bool = True):
        super(JWTBearer, self).__init__(auto_error=auto_error)
    
    async def __call__(self, credentials: HTTPAuthorizationCredentials = Depends(security)):
        if credentials:
            if not credentials.scheme == "Bearer":
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Schéma d'authentification invalide"
                )
            if not self.verify_jwt(credentials.credentials):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token invalide ou expiré"
                )
            return credentials.credentials
        else:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token d'authentification requis"
            )
    
    def verify_jwt(self, token: str) -> bool:
        """Vérifie la validité du token JWT."""
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
            return True
        except JWTError:
            return False


# Instance du validateur JWT
jwt_bearer = JWTBearer()


async def get_current_user(
    token: str = Depends(jwt_bearer),
    auth_service: AuthService = Depends(get_auth_service)
) -> AdminUser:
    """
    Dépendance pour récupérer l'utilisateur actuel à partir du token JWT.
    
    Args:
        token: Token JWT extrait de l'en-tête Authorization
        auth_service: Service d'authentification
        
    Returns:
        AdminUser: Utilisateur authentifié
        
    Raises:
        HTTPException: Si le token est invalide ou l'utilisateur n'existe pas
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Impossible de valider les credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # Décoder le token
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except JWTError:
        raise credentials_exception
    
    # Récupérer l'utilisateur
    user = auth_service.get_user(username=token_data.username)
    if user is None:
        raise credentials_exception
    
    return user


async def get_current_active_user(
    current_user: AdminUser = Depends(get_current_user)
) -> AdminUser:
    """
    Dépendance pour récupérer l'utilisateur actuel actif.
    
    Args:
        current_user: Utilisateur actuel
        
    Returns:
        AdminUser: Utilisateur actif
        
    Raises:
        HTTPException: Si l'utilisateur est inactif
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Utilisateur inactif"
        )
    return current_user


async def get_current_superuser(
    current_user: AdminUser = Depends(get_current_active_user)
) -> AdminUser:
    """
    Dépendance pour récupérer l'utilisateur actuel super-utilisateur.
    
    Args:
        current_user: Utilisateur actuel actif
        
    Returns:
        AdminUser: Super-utilisateur
        
    Raises:
        HTTPException: Si l'utilisateur n'est pas super-utilisateur
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Privilèges insuffisants"
        )
    return current_user


def require_roles(*required_roles: str):
    """
    Décorateur pour exiger des rôles spécifiques.
    
    Args:
        *required_roles: Rôles requis
        
    Returns:
        Fonction de dépendance FastAPI
    """
    async def check_roles(
        current_user: AdminUser = Depends(get_current_active_user)
    ) -> AdminUser:
        if not any(role in current_user.roles for role in required_roles):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Rôles requis: {', '.join(required_roles)}"
            )
        return current_user
    
    return check_roles


def require_permissions(*required_permissions: str):
    """
    Décorateur pour exiger des permissions spécifiques.
    
    Args:
        *required_permissions: Permissions requises
        
    Returns:
        Fonction de dépendance FastAPI
    """
    async def check_permissions(
        current_user: AdminUser = Depends(get_current_active_user)
    ) -> AdminUser:
        # Pour l'instant, on utilise les rôles comme permissions
        # Plus tard, on pourra implémenter un système de permissions plus granulaire
        user_permissions = current_user.roles.copy()
        
        # Les super-utilisateurs ont toutes les permissions
        if current_user.is_superuser:
            user_permissions.append("admin:all")
        
        if not any(perm in user_permissions for perm in required_permissions):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permissions requises: {', '.join(required_permissions)}"
            )
        return current_user
    
    return check_permissions


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Crée un token JWT d'accès.
    
    Args:
        data: Données à encoder dans le token
        expires_delta: Durée de validité personnalisée
        
    Returns:
        str: Token JWT encodé
    """
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def verify_token(token: str) -> Optional[dict]:
    """
    Vérifie et décode un token JWT.
    
    Args:
        token: Token JWT à vérifier
        
    Returns:
        dict: Payload du token si valide, None sinon
    """
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            return None
        return payload
    except JWTError:
        return None
