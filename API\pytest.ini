[tool:pytest]
# Configuration pytest pour Notiflair API

# Répertoires de tests
testpaths = tests

# Patterns de fichiers de test
python_files = test_*.py *_test.py

# Patterns de classes de test
python_classes = Test*

# Patterns de fonctions de test
python_functions = test_*

# Marqueurs personnalisés
markers =
    slow: marque les tests comme lents (désélectionne avec '-m "not slow"')
    integration: tests d'intégration
    unit: tests unitaires
    auth: tests d'authentification
    sync: tests de synchronisation
    notifications: tests de notifications
    admin: tests d'administration
    database: tests nécessitant une base de données

# Options par défaut
addopts = 
    -v
    --strict-markers
    --strict-config
    --tb=short
    --cov=app
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-fail-under=80

# Filtres d'avertissements
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:.*unclosed.*:ResourceWarning

# Configuration de logging pour les tests
log_cli = false
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Timeout pour les tests (en secondes)
timeout = 300

# Répertoire temporaire
tmp_path_retention_count = 3
tmp_path_retention_policy = failed

# Configuration de couverture
[coverage:run]
source = app
omit = 
    */tests/*
    */venv/*
    */env/*
    */__pycache__/*
    */migrations/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod
