#!/usr/bin/env python3
"""
Script de démonstration des endpoints de référence

Ce script démontre l'utilisation des endpoints créés pour consulter
les pays et leurs langues associées.
"""

import json
import requests
from typing import Dict, Any


def pretty_print_json(data: Dict[str, Any], title: str = ""):
    """Affiche du JSON de manière formatée."""
    if title:
        print(f"\n{'='*60}")
        print(f"  {title}")
        print(f"{'='*60}")
    
    print(json.dumps(data, indent=2, ensure_ascii=False))


def test_endpoint(url: str, description: str):
    """Teste un endpoint et affiche le résultat."""
    try:
        print(f"\n🔍 Test: {description}")
        print(f"📡 URL: {url}")
        
        response = requests.get(url)
        
        if response.status_code == 200:
            print(f"✅ Statut: {response.status_code} OK")
            data = response.json()
            pretty_print_json(data)
        else:
            print(f"❌ Erreur: {response.status_code}")
            print(f"Message: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Impossible de se connecter à {url}")
        print("💡 Assurez-vous que l'API est démarrée avec: python main.py")
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")


def main():
    """Fonction principale de démonstration."""
    base_url = "http://localhost:8000/api/v1/reference"
    
    print("🔔 Démonstration des endpoints de référence Notiflair")
    print("=" * 60)
    
    # Test 1: Récupération de tous les pays
    test_endpoint(
        f"{base_url}/countries",
        "Récupération de tous les pays avec leurs langues"
    )
    
    # Test 2: Récupération d'un pays spécifique
    test_endpoint(
        f"{base_url}/countries/BE",
        "Récupération des détails de la Belgique"
    )
    
    # Test 3: Récupération d'un pays inexistant
    test_endpoint(
        f"{base_url}/countries/XX",
        "Test avec un pays inexistant (devrait retourner 404)"
    )
    
    # Test 4: Récupération de toutes les langues
    test_endpoint(
        f"{base_url}/languages",
        "Récupération de toutes les langues disponibles"
    )
    
    # Test 5: Statistiques
    test_endpoint(
        f"{base_url}/stats",
        "Statistiques des données de référence"
    )
    
    print(f"\n{'='*60}")
    print("✅ Démonstration terminée!")
    print("\n💡 Pour explorer l'API interactivement:")
    print("   👉 Ouvrez http://localhost:8000/docs dans votre navigateur")
    print(f"{'='*60}")


if __name__ == "__main__":
    main()
