"""
Schémas Pydantic pour l'authentification admin

Ces schémas définissent les structures de données pour l'authentification,
les tokens JWT et les utilisateurs admin.
"""

from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, EmailStr, Field, validator


class LoginRequest(BaseModel):
    """Schéma pour la requête de connexion."""
    username: str = Field(..., min_length=3, max_length=50, description="Nom d'utilisateur ou email")
    password: str = Field(..., min_length=1, max_length=100, description="Mot de passe")
    
    @validator('username')
    def validate_username(cls, v):
        """Valide le nom d'utilisateur."""
        if not v or v.isspace():
            raise ValueError('Le nom d\'utilisateur ne peut pas être vide')
        return v.strip().lower()
    
    @validator('password')
    def validate_password(cls, v):
        """Valide le mot de passe."""
        if not v or v.isspace():
            raise ValueError('Le mot de passe ne peut pas être vide')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "username": "admin",
                "password": "secret"
            }
        }


class Token(BaseModel):
    """Schéma pour la réponse de token JWT."""
    access_token: str = Field(..., description="Token JWT d'accès")
    token_type: str = Field(default="bearer", description="Type de token")
    expires_in: int = Field(..., description="Durée de validité en secondes")
    
    class Config:
        schema_extra = {
            "example": {
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "token_type": "bearer",
                "expires_in": 3600
            }
        }


class TokenData(BaseModel):
    """Schéma pour les données contenues dans le token."""
    username: Optional[str] = None
    scopes: List[str] = []


class AdminUserBase(BaseModel):
    """Schéma de base pour un utilisateur admin."""
    username: str = Field(..., min_length=3, max_length=50, description="Nom d'utilisateur unique")
    email: EmailStr = Field(..., description="Adresse email")
    full_name: str = Field(..., min_length=1, max_length=100, description="Nom complet")
    is_active: bool = Field(default=True, description="Utilisateur actif")
    is_superuser: bool = Field(default=False, description="Super utilisateur")
    roles: List[str] = Field(default=[], description="Rôles de l'utilisateur")


class AdminUserCreate(AdminUserBase):
    """Schéma pour créer un utilisateur admin."""
    password: str = Field(..., min_length=6, max_length=100, description="Mot de passe")
    
    @validator('password')
    def validate_password(cls, v):
        """Valide le mot de passe."""
        if len(v) < 6:
            raise ValueError('Le mot de passe doit contenir au moins 6 caractères')
        return v


class AdminUserUpdate(BaseModel):
    """Schéma pour mettre à jour un utilisateur admin."""
    email: Optional[EmailStr] = None
    full_name: Optional[str] = Field(None, min_length=1, max_length=100)
    is_active: Optional[bool] = None
    is_superuser: Optional[bool] = None
    roles: Optional[List[str]] = None
    password: Optional[str] = Field(None, min_length=6, max_length=100)


class AdminUserResponse(AdminUserBase):
    """Schéma pour la réponse d'un utilisateur admin (sans mot de passe)."""
    id: int = Field(..., description="Identifiant unique")
    created_at: str = Field(..., description="Date de création")
    last_login: Optional[str] = Field(None, description="Dernière connexion")
    
    class Config:
        from_attributes = True
        schema_extra = {
            "example": {
                "id": 1,
                "username": "admin",
                "email": "<EMAIL>",
                "full_name": "Administrateur Principal",
                "is_active": True,
                "is_superuser": True,
                "roles": ["admin", "superuser"],
                "created_at": "2025-06-24T10:00:00Z",
                "last_login": "2025-06-24T12:30:00Z"
            }
        }


class AdminUserMe(AdminUserResponse):
    """Schéma pour les informations de l'utilisateur connecté."""
    permissions: List[str] = Field(default=[], description="Permissions de l'utilisateur")
    
    class Config:
        schema_extra = {
            "example": {
                "id": 1,
                "username": "admin",
                "email": "<EMAIL>",
                "full_name": "Administrateur Principal",
                "is_active": True,
                "is_superuser": True,
                "roles": ["admin", "superuser"],
                "created_at": "2025-06-24T10:00:00Z",
                "last_login": "2025-06-24T12:30:00Z",
                "permissions": ["read:users", "write:users", "delete:users", "admin:all"]
            }
        }


class LoginResponse(BaseModel):
    """Schéma pour la réponse complète de connexion."""
    access_token: str = Field(..., description="Token JWT d'accès")
    token_type: str = Field(default="bearer", description="Type de token")
    expires_in: int = Field(..., description="Durée de validité en secondes")
    user: AdminUserResponse = Field(..., description="Informations de l'utilisateur")
    
    class Config:
        schema_extra = {
            "example": {
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "token_type": "bearer",
                "expires_in": 3600,
                "user": {
                    "id": 1,
                    "username": "admin",
                    "email": "<EMAIL>",
                    "full_name": "Administrateur Principal",
                    "is_active": True,
                    "is_superuser": True,
                    "roles": ["admin", "superuser"],
                    "created_at": "2025-06-24T10:00:00Z",
                    "last_login": None
                }
            }
        }


class ErrorResponse(BaseModel):
    """Schéma pour les réponses d'erreur."""
    error: bool = Field(default=True, description="Indicateur d'erreur")
    message: str = Field(..., description="Message d'erreur")
    status_code: int = Field(..., description="Code de statut HTTP")
    path: Optional[str] = Field(None, description="Chemin de la requête")
    
    class Config:
        schema_extra = {
            "example": {
                "error": True,
                "message": "Nom d'utilisateur ou mot de passe incorrect",
                "status_code": 401,
                "path": "/api/v1/auth/login"
            }
        }
