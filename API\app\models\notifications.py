"""
Modèles SQLAlchemy pour les notifications et types de notifications

Tables liées aux notifications, types, keywords et leurs traductions.
"""

from datetime import datetime
from typing import List, Optional
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, DECIMAL, JSON
from sqlalchemy.dialects.mysql import TINYINT
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import func

from app.database import Base
from app.models.reference import notification_keyword_association, Country, Language


class NotificationType(Base):
    """
    Modèle pour la table tr_notif_types_ntp
    
    Types/catégories de notifications avec support de hiérarchie.
    """
    __tablename__ = 'tr_notif_types_ntp'
    
    # Colonnes
    ntp_id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    ntp_identifier_key: Mapped[str] = mapped_column(String(50), unique=True, nullable=False, index=True)
    ntp_icon: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    ntp_color: Mapped[Optional[str]] = mapped_column(String(7), nullable=True)  # Couleur hexa
    ntp_bg_color: Mapped[Optional[str]] = mapped_column(String(7), nullable=True)  # Couleur de fond
    ntp_display_order: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    ntp_parent_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey('tr_notif_types_ntp.ntp_id'), nullable=True)
    ntp_level: Mapped[Optional[int]] = mapped_column(TINYINT, nullable=True)  # 0=racine, 1=enfant...
    ntp_description_template: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    ntp_default_enabled: Mapped[Optional[bool]] = mapped_column(Boolean, default=True, nullable=True)
    
    # Relations hiérarchiques
    parent: Mapped[Optional["NotificationType"]] = relationship(
        "NotificationType",
        remote_side=[ntp_id],
        back_populates="children"
    )
    children: Mapped[List["NotificationType"]] = relationship(
        "NotificationType",
        back_populates="parent"
    )
    
    # Relations vers les notifications
    notifications: Mapped[List["Notification"]] = relationship(
        "Notification",
        back_populates="notification_type"
    )
    
    # Relations vers les traductions (nouvelle architecture)
    translations: Mapped[List["NotificationTypeTranslation"]] = relationship(
        "NotificationTypeTranslation",
        back_populates="notification_type"
    )
    # Note: Les overrides sont maintenant accessibles via translations.overrides
    # car ils référencent directement les traductions via ntt_id
    
    def __repr__(self) -> str:
        return f"<NotificationType(id={self.ntp_id}, key='{self.ntp_identifier_key}', level={self.ntp_level})>"


class Keyword(Base):
    """
    Modèle pour la table tr_keywords_kwd
    
    Concepts de keywords indépendants de la langue.
    """
    __tablename__ = 'tr_keywords_kwd'
    
    # Colonnes
    kwd_id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    kwd_identifier: Mapped[str] = mapped_column(String(50), unique=True, nullable=False, index=True)
    kwd_created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), nullable=False)
    
    # Relations
    notifications: Mapped[List["Notification"]] = relationship(
        "Notification",
        secondary=notification_keyword_association,
        back_populates="keywords"
    )
    
    # Relations vers les traductions
    translations: Mapped[List["KeywordTranslation"]] = relationship(
        "KeywordTranslation",
        back_populates="keyword"
    )
    
    def __repr__(self) -> str:
        return f"<Keyword(id={self.kwd_id}, identifier='{self.kwd_identifier}')>"


class Notification(Base):
    """
    Modèle pour la table t_notifs_ntf
    
    Table principale des notifications/événements.
    """
    __tablename__ = 't_notifs_ntf'
    
    # Colonnes
    ntf_id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    cty_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey('tr_countries_cty.cty_id'), nullable=True)
    ntp_id: Mapped[int] = mapped_column(Integer, ForeignKey('tr_notif_types_ntp.ntp_id'), nullable=False)
    ntf_identifier_key: Mapped[str] = mapped_column(String(100), unique=True, nullable=False, index=True)
    ntf_is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    ntf_rrule: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)  # Règle RFC 5545
    ntf_rrule_text: Mapped[Optional[str]] = mapped_column(Text, nullable=True)  # Description humaine
    ntf_icon: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)  # Override de l'icône
    ntf_usage_count: Mapped[Optional[int]] = mapped_column(Integer, default=0, nullable=True)
    ntf_needs_annual_update: Mapped[Optional[bool]] = mapped_column(Boolean, default=False, nullable=True)
    ntf_last_updated_year: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    ntf_default_reminder: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)  # Config rappel
    ntf_relevance_score: Mapped[Optional[float]] = mapped_column(DECIMAL(3, 2), nullable=True)  # Score 0.00-9.99
    
    # Relations
    country: Mapped[Optional[Country]] = relationship("Country", viewonly=True)
    notification_type: Mapped[NotificationType] = relationship(
        "NotificationType",
        back_populates="notifications"
    )
    
    # Relations vers les keywords
    keywords: Mapped[List[Keyword]] = relationship(
        "Keyword",
        secondary=notification_keyword_association,
        back_populates="notifications"
    )
    
    # Relations vers les traductions (nouvelle architecture)
    translations: Mapped[List["NotificationTranslation"]] = relationship(
        "NotificationTranslation",
        back_populates="notification"
    )
    # Note: Les overrides sont maintenant accessibles via translations.overrides
    # car ils référencent directement les traductions via ntr_id
    
    def __repr__(self) -> str:
        return f"<Notification(id={self.ntf_id}, key='{self.ntf_identifier_key}', active={self.ntf_is_active})>"


# Tables de traductions

class NotificationTranslation(Base):
    """
    Modèle pour la table t_notif_translations_ntr ⭐ STRUCTURE CORRIGÉE

    Traductions des notifications par LANGUE uniquement (post-correction 2025).
    Utilise lng_id pour les traductions universelles par langue.
    """
    __tablename__ = 't_notif_translations_ntr'

    # Colonnes (structure corrigée avec lng_id)
    ntr_id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    lng_id: Mapped[int] = mapped_column(Integer, ForeignKey('tr_languages_lng.lng_id'), nullable=False)
    ntf_id: Mapped[int] = mapped_column(Integer, ForeignKey('t_notifs_ntf.ntf_id'), nullable=False)
    ntr_label: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    ntr_description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    ntr_created_at: Mapped[Optional[datetime]] = mapped_column(DateTime, default=func.now())
    ntr_updated_at: Mapped[Optional[datetime]] = mapped_column(DateTime, default=func.now(), onupdate=func.now())

    # Relations
    notification: Mapped[Notification] = relationship("Notification", back_populates="translations")
    language: Mapped[Language] = relationship("Language", viewonly=True)

    # Relation vers les overrides qui référencent cette traduction
    overrides: Mapped[List["NotificationTranslationOverride"]] = relationship(
        "NotificationTranslationOverride",
        back_populates="translation"
    )

    def __repr__(self) -> str:
        return f"<NotificationTranslation(ntr_id={self.ntr_id}, lng_id={self.lng_id}, ntf_id={self.ntf_id}, label='{self.ntr_label}')>"


class NotificationTranslationOverride(Base):
    """
    Modèle pour la table t_notif_trans_ovr_nov ⭐ STRUCTURE CORRIGÉE

    Overrides des traductions de notifications par PAYS-LANGUE (post-correction 2025).
    Référence une traduction (ntr_id) + une combinaison pays-langue (ctl_id).
    """
    __tablename__ = 't_notif_trans_ovr_nov'

    # Colonnes (structure corrigée avec ctl_id)
    nov_id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    ntr_id: Mapped[int] = mapped_column(Integer, ForeignKey('t_notif_translations_ntr.ntr_id'), nullable=False)
    ctl_id: Mapped[int] = mapped_column(Integer, ForeignKey('tj_country_languages_ctl.ctl_id'), nullable=False)
    nov_label: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    nov_description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    nov_created_at: Mapped[Optional[datetime]] = mapped_column(DateTime, default=func.now())
    nov_updated_at: Mapped[Optional[datetime]] = mapped_column(DateTime, default=func.now(), onupdate=func.now())

    # Relations
    translation: Mapped[NotificationTranslation] = relationship("NotificationTranslation", back_populates="overrides")
    country_language: Mapped["CountryLanguage"] = relationship("CountryLanguage", viewonly=True)

    def __repr__(self) -> str:
        return f"<NotificationTranslationOverride(nov_id={self.nov_id}, ntr_id={self.ntr_id}, ctl_id={self.ctl_id}, label='{self.nov_label}')>"


class NotificationTypeTranslation(Base):
    """
    Modèle pour la table t_notif_type_trans_ntt ⭐ STRUCTURE CORRIGÉE

    Traductions des types de notifications par LANGUE uniquement (post-correction 2025).
    Utilise lng_id pour les traductions universelles par langue.
    """
    __tablename__ = 't_notif_type_trans_ntt'

    # Colonnes (structure corrigée avec lng_id)
    ntt_id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    lng_id: Mapped[int] = mapped_column(Integer, ForeignKey('tr_languages_lng.lng_id'), nullable=False)
    ntp_id: Mapped[int] = mapped_column(Integer, ForeignKey('tr_notif_types_ntp.ntp_id'), nullable=False)
    ntt_label: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    ntt_description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    ntt_created_at: Mapped[Optional[datetime]] = mapped_column(DateTime, default=func.now())
    ntt_updated_at: Mapped[Optional[datetime]] = mapped_column(DateTime, default=func.now(), onupdate=func.now())

    # Relations
    notification_type: Mapped[NotificationType] = relationship("NotificationType", back_populates="translations")
    language: Mapped[Language] = relationship("Language", viewonly=True)

    # Relation vers les overrides qui référencent cette traduction
    overrides: Mapped[List["NotificationTypeTranslationOverride"]] = relationship(
        "NotificationTypeTranslationOverride",
        back_populates="translation"
    )

    def __repr__(self) -> str:
        return f"<NotificationTypeTranslation(ntt_id={self.ntt_id}, lng_id={self.lng_id}, ntp_id={self.ntp_id}, label='{self.ntt_label}')>"


class NotificationTypeTranslationOverride(Base):
    """
    Modèle pour la table t_notif_type_trans_ovr_nto ⭐ STRUCTURE CORRIGÉE

    Overrides des traductions de types de notifications par PAYS-LANGUE (post-correction 2025).
    Référence une traduction (ntt_id) + une combinaison pays-langue (ctl_id).
    """
    __tablename__ = 't_notif_type_trans_ovr_nto'

    # Colonnes (structure corrigée avec ctl_id)
    nto_id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    ntt_id: Mapped[int] = mapped_column(Integer, ForeignKey('t_notif_type_trans_ntt.ntt_id'), nullable=False)
    ctl_id: Mapped[int] = mapped_column(Integer, ForeignKey('tj_country_languages_ctl.ctl_id'), nullable=False)
    nto_label: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    nto_description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    nto_created_at: Mapped[Optional[datetime]] = mapped_column(DateTime, default=func.now())
    nto_updated_at: Mapped[Optional[datetime]] = mapped_column(DateTime, default=func.now(), onupdate=func.now())

    # Relations
    translation: Mapped[NotificationTypeTranslation] = relationship("NotificationTypeTranslation", back_populates="overrides")
    country_language: Mapped["CountryLanguage"] = relationship("CountryLanguage", viewonly=True)

    def __repr__(self) -> str:
        return f"<NotificationTypeTranslationOverride(nto_id={self.nto_id}, ntt_id={self.ntt_id}, ctl_id={self.ctl_id}, label='{self.nto_label}')>"


class KeywordTranslation(Base):
    """
    Modèle pour la table t_keyword_translations_kwt
    
    Traductions des keywords.
    """
    __tablename__ = 't_keyword_translations_kwt'
    
    # Colonnes (clé primaire composite)
    kwd_id: Mapped[int] = mapped_column(Integer, ForeignKey('tr_keywords_kwd.kwd_id'), primary_key=True)
    lng_id: Mapped[int] = mapped_column(Integer, ForeignKey('tr_languages_lng.lng_id'), primary_key=True)
    kwt_term: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    
    # Relations
    keyword: Mapped[Keyword] = relationship("Keyword", back_populates="translations")
    language: Mapped[Language] = relationship("Language", viewonly=True)
    
    def __repr__(self) -> str:
        return f"<KeywordTranslation(kwd_id={self.kwd_id}, lng_id={self.lng_id}, term='{self.kwt_term}')>"
