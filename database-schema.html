<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notiflair - Schéma de Base de Données MySQL</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            padding: 40px 0;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .architecture-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            margin-bottom: 30px;
        }

        .architecture-info h3 {
            color: #fff;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .architecture-info ul {
            list-style: none;
            color: rgba(255, 255, 255, 0.9);
        }

        .architecture-info li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }

        .architecture-info li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4CAF50;
            font-weight: bold;
        }

        .schema-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .table-group {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .table-group:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .group-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 3px solid;
        }

        .group-icon {
            font-size: 2rem;
            margin-right: 15px;
        }

        .group-title {
            font-size: 1.4rem;
            font-weight: 600;
        }

        .group-description {
            font-size: 0.9rem;
            color: #666;
            margin-top: 5px;
        }

        /* Couleurs par groupe */
        .reference .group-header {
            border-color: #3498db;
        }

        .reference .group-icon {
            color: #3498db;
        }

        .core .group-header {
            border-color: #e74c3c;
        }

        .core .group-icon {
            color: #e74c3c;
        }

        .translations .group-header {
            border-color: #f39c12;
        }

        .translations .group-icon {
            color: #f39c12;
        }

        .app-translations .group-header {
            border-color: #9b59b6;
        }

        .app-translations .group-icon {
            color: #9b59b6;
        }

        .sync .group-header {
            border-color: #2ecc71;
        }

        .sync .group-icon {
            color: #2ecc71;
        }

        .table-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid;
            transition: all 0.3s ease;
        }

        .table-item:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }

        .reference .table-item {
            border-left-color: #3498db;
        }

        .core .table-item {
            border-left-color: #e74c3c;
        }

        .translations .table-item {
            border-left-color: #f39c12;
        }

        .app-translations .table-item {
            border-left-color: #9b59b6;
        }

        .sync .table-item {
            border-left-color: #2ecc71;
        }

        .table-name {
            font-weight: 600;
            font-size: 1rem;
            color: #2c3e50;
            margin-bottom: 5px;
            font-family: 'Courier New', monospace;
        }

        .table-description {
            font-size: 0.85rem;
            color: #666;
            margin-bottom: 8px;
        }

        .table-columns {
            font-size: 0.75rem;
            color: #888;
            font-style: italic;
        }

        .key-info {
            display: flex;
            gap: 10px;
            margin-top: 8px;
        }

        .key-badge {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .pk {
            background: #e3f2fd;
            color: #1976d2;
        }

        .fk {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .uk {
            background: #e8f5e8;
            color: #388e3c;
        }

        .relations-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .relations-title {
            font-size: 1.8rem;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        .relation-flow {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.8;
            color: #495057;
            overflow-x: auto;
        }

        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: 600;
        }

        .footer {
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 40px;
            padding: 20px;
        }

        @media (max-width: 768px) {
            .schema-grid {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2rem;
            }

            .container {
                padding: 10px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🔔 Notiflair</h1>
            <div class="subtitle">Schéma de Base de Données MySQL</div>

            <div class="architecture-info">
                <h3>🏗️ Architecture Post-Migration 2025</h3>
                <ul>
                    <li><strong>Pivot Central :</strong> tj_country_languages_ctl avec ctl_id unique</li>
                    <li><strong>Traductions :</strong> Par langue uniquement (lng_id)</li>
                    <li><strong>Overrides :</strong> Par pays-langue (ctl_id)</li>
                    <li><strong>Fallback :</strong> Override → Traduction → Clé technique</li>
                    <li><strong>Performance :</strong> Réduction des jointures de 3 à 1 table</li>
                </ul>
            </div>
        </div>

        <div class="schema-grid">
            <!-- Tables de Référence -->
            <div class="table-group reference">
                <div class="group-header">
                    <div class="group-icon">🌍</div>
                    <div>
                        <div class="group-title">Tables de Référence</div>
                        <div class="group-description">Données statiques et configuration</div>
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-name">tr_countries_cty</div>
                    <div class="table-description">Pays supportés par l'application</div>
                    <div class="table-columns">cty_id, cty_code, cty_name, cty_timezone, cty_default_lng_id</div>
                    <div class="key-info">
                        <span class="key-badge pk">PK: cty_id</span>
                        <span class="key-badge uk">UK: cty_code</span>
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-name">tr_languages_lng</div>
                    <div class="table-description">Langues supportées</div>
                    <div class="table-columns">lng_id, lng_code, lng_name, lng_is_active</div>
                    <div class="key-info">
                        <span class="key-badge pk">PK: lng_id</span>
                        <span class="key-badge uk">UK: lng_code</span>
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-name">tj_country_languages_ctl</div>
                    <div class="table-description">⭐ PIVOT CENTRAL - Combinaisons pays-langue</div>
                    <div class="table-columns">ctl_id, cty_id, lng_id</div>
                    <div class="key-info">
                        <span class="key-badge pk">PK: ctl_id</span>
                        <span class="key-badge fk">FK: cty_id, lng_id</span>
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-name">tr_notif_types_ntp</div>
                    <div class="table-description">Types/catégories de notifications</div>
                    <div class="table-columns">ntp_id, ntp_identifier_key, ntp_parent_id, ntp_is_active</div>
                    <div class="key-info">
                        <span class="key-badge pk">PK: ntp_id</span>
                        <span class="key-badge fk">FK: ntp_parent_id</span>
                    </div>
                </div>
            </div>

            <!-- Tables Core -->
            <div class="table-group core">
                <div class="group-header">
                    <div class="group-icon">🔔</div>
                    <div>
                        <div class="group-title">Tables Core</div>
                        <div class="group-description">Données métier principales</div>
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-name">t_notifications_ntf</div>
                    <div class="table-description">Notifications/rappels principaux</div>
                    <div class="table-columns">ntf_id, ntf_identifier_key, cty_id, ntp_id, ntf_is_active, ntf_rrule
                    </div>
                    <div class="key-info">
                        <span class="key-badge pk">PK: ntf_id</span>
                        <span class="key-badge fk">FK: cty_id, ntp_id</span>
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-name">tr_keywords_kwd</div>
                    <div class="table-description">Mots-clés pour recherche et catégorisation</div>
                    <div class="table-columns">kwd_id, kwd_identifier_key, kwd_is_active</div>
                    <div class="key-info">
                        <span class="key-badge pk">PK: kwd_id</span>
                        <span class="key-badge uk">UK: kwd_identifier_key</span>
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-name">tj_ntf_keywords_nkw</div>
                    <div class="table-description">Association notifications ↔ keywords</div>
                    <div class="table-columns">ntf_id, kwd_id</div>
                    <div class="key-info">
                        <span class="key-badge pk">PK: ntf_id, kwd_id</span>
                        <span class="key-badge fk">FK: ntf_id, kwd_id</span>
                    </div>
                </div>
            </div>

            <!-- Tables de Traductions -->
            <div class="table-group translations">
                <div class="group-header">
                    <div class="group-icon">🌐</div>
                    <div>
                        <div class="group-title">Traductions Notifications</div>
                        <div class="group-description">Système multilingue avec fallback</div>
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-name">t_ntf_translations_ntr</div>
                    <div class="table-description">Traductions par LANGUE (lng_id)</div>
                    <div class="table-columns">ntr_id, ntf_id, lng_id, ntr_label, ntr_description</div>
                    <div class="key-info">
                        <span class="key-badge pk">PK: ntr_id</span>
                        <span class="key-badge fk">FK: ntf_id, lng_id</span>
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-name">t_ntf_translations_override_nov</div>
                    <div class="table-description">Overrides par PAYS-LANGUE (ctl_id)</div>
                    <div class="table-columns">nov_id, ntr_id, ctl_id, nov_label, nov_description</div>
                    <div class="key-info">
                        <span class="key-badge pk">PK: nov_id</span>
                        <span class="key-badge fk">FK: ntr_id, ctl_id</span>
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-name">t_ntp_translations_ntt</div>
                    <div class="table-description">Traductions types par LANGUE (lng_id)</div>
                    <div class="table-columns">ntt_id, ntp_id, lng_id, ntt_label, ntt_description</div>
                    <div class="key-info">
                        <span class="key-badge pk">PK: ntt_id</span>
                        <span class="key-badge fk">FK: ntp_id, lng_id</span>
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-name">t_ntp_translations_override_nto</div>
                    <div class="table-description">Overrides types par PAYS-LANGUE (ctl_id)</div>
                    <div class="table-columns">nto_id, ntt_id, ctl_id, nto_label, nto_description</div>
                    <div class="key-info">
                        <span class="key-badge pk">PK: nto_id</span>
                        <span class="key-badge fk">FK: ntt_id, ctl_id</span>
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-name">t_nkw_translations_kwt</div>
                    <div class="table-description">Traductions keywords par LANGUE (lng_id)</div>
                    <div class="table-columns">kwt_id, kwd_id, lng_id, kwt_label</div>
                    <div class="key-info">
                        <span class="key-badge pk">PK: kwt_id</span>
                        <span class="key-badge fk">FK: kwd_id, lng_id</span>
                    </div>
                </div>
            </div>

            <!-- Tables de Traductions d'Application -->
            <div class="table-group app-translations">
                <div class="group-header">
                    <div class="group-icon">📱</div>
                    <div>
                        <div class="group-title">Traductions d'Application</div>
                        <div class="group-description">Interface utilisateur multilingue</div>
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-name">t_app_translations_atr</div>
                    <div class="table-description">Clés de traduction d'interface</div>
                    <div class="table-columns">atr_id, atr_key, atr_context</div>
                    <div class="key-info">
                        <span class="key-badge pk">PK: atr_id</span>
                        <span class="key-badge uk">UK: atr_key</span>
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-name">t_atr_translations_atv</div>
                    <div class="table-description">Valeurs par LANGUE (lng_id)</div>
                    <div class="table-columns">atv_id, atr_id, lng_id, atv_value</div>
                    <div class="key-info">
                        <span class="key-badge pk">PK: atv_id</span>
                        <span class="key-badge fk">FK: atr_id, lng_id</span>
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-name">t_atv_override_ato</div>
                    <div class="table-description">Overrides par PAYS-LANGUE (ctl_id)</div>
                    <div class="table-columns">ato_id, atr_id, ctl_id, ato_value</div>
                    <div class="key-info">
                        <span class="key-badge pk">PK: ato_id</span>
                        <span class="key-badge fk">FK: atr_id, ctl_id</span>
                    </div>
                </div>
            </div>

            <!-- Tables de Synchronisation -->
            <div class="table-group sync">
                <div class="group-header">
                    <div class="group-icon">🔄</div>
                    <div>
                        <div class="group-title">Synchronisation Mobile</div>
                        <div class="group-description">Versioning et snapshots</div>
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-name">t_translation_versions_tvr</div>
                    <div class="table-description">Versions par pays-langue</div>
                    <div class="table-columns">tvr_id, cty_id, lng_id, tvr_current_version, tvr_last_published</div>
                    <div class="key-info">
                        <span class="key-badge pk">PK: tvr_id</span>
                        <span class="key-badge fk">FK: cty_id, lng_id</span>
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-name">t_sync_snapshots_sss</div>
                    <div class="table-description">Snapshots JSON compressés</div>
                    <div class="table-columns">sss_id, cty_id, lng_id, sss_version, sss_data, sss_checksum</div>
                    <div class="key-info">
                        <span class="key-badge pk">PK: sss_id</span>
                        <span class="key-badge fk">FK: cty_id, lng_id</span>
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-name">t_users_usr</div>
                    <div class="table-description">Utilisateurs mobiles</div>
                    <div class="table-columns">usr_id, usr_device_id, usr_last_sync, usr_app_version</div>
                    <div class="key-info">
                        <span class="key-badge pk">PK: usr_id</span>
                        <span class="key-badge uk">UK: usr_device_id</span>
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-name">t_migrations_mig</div>
                    <div class="table-description">Historique des migrations</div>
                    <div class="table-columns">mig_id, mig_version, mig_description, mig_executed_at, mig_status</div>
                    <div class="key-info">
                        <span class="key-badge pk">PK: mig_id</span>
                        <span class="key-badge uk">UK: mig_version</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Relations -->
        <div class="relations-section">
            <h2 class="relations-title">🔗 Architecture des Relations</h2>
            <div class="relation-flow">
                <strong>FLUX PRINCIPAL :</strong>
                Pays + Langue → <span class="highlight">tj_country_languages_ctl (ctl_id)</span> → Traductions →
                Overrides

                <strong>TRADUCTIONS (par langue uniquement) :</strong>
                ├── t_ntf_translations_ntr (ntf_id + <span class="highlight">lng_id</span>)
                ├── t_ntp_translations_ntt (ntp_id + <span class="highlight">lng_id</span>)
                ├── t_nkw_translations_kwt (kwd_id + <span class="highlight">lng_id</span>)
                └── t_atr_translations_atv (atr_id + <span class="highlight">lng_id</span>)

                <strong>OVERRIDES (par pays-langue) :</strong>
                ├── t_ntf_translations_override_nov (ntr_id + <span class="highlight">ctl_id</span>)
                ├── t_ntp_translations_override_nto (ntt_id + <span class="highlight">ctl_id</span>)
                └── t_atv_override_ato (atr_id + <span class="highlight">ctl_id</span>)

                <strong>LOGIQUE DE FALLBACK :</strong>
                1. Override spécifique (pays-langue) → <span class="highlight">ctl_id</span>
                2. Traduction langue → <span class="highlight">lng_id</span>
                3. Clé technique → <span class="highlight">identifier_key</span>
            </div>
        </div>

        <div class="footer">
            <p>📊 Base de données Notiflair - Architecture Post-Migration 2025</p>
            <p>🚀 Optimisée pour performance et simplicité</p>
        </div>
    </div>
</body>

</html>