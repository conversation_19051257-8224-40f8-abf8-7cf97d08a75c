@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  font-family: 'Inter', sans-serif;
}

/* Scrollbar personnalisée */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Animation du gradient */
.gradient-animated {
  background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #1e293b 100%);
  background-size: 200% 200%;
  animation: gradient 15s ease infinite;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Animation spin rapide */
.animate-spin-fast {
  animation: spin 0.5s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Focus styles personnalisés */
.input-focus:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Glow effect */
.glow {
  box-shadow: 0 0 20px rgba(37, 99, 235, 0.3);
}

/* Pattern de fond */
.pattern-bg {
  background-color: #0f172a;
  background-image:
    radial-gradient(at 47% 33%, hsl(222.2, 47.4%, 11.2%) 0, transparent 59%),
    radial-gradient(at 82% 65%, hsl(218, 39%, 15%) 0, transparent 55%);
}

/* Menu item actif */
.menu-active {
  background-color: rgba(59, 130, 246, 0.1);
  border-left: 3px solid #2563eb;
}

.menu-active span {
  color: #3b82f6;
  font-weight: 600;
}

.menu-active svg {
  color: #3b82f6;
}

/* Badge pulse */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.05); }
  100% { opacity: 1; transform: scale(1); }
}

/* Tabs */
.tab-active {
  color: #2563eb;
  border-bottom: 2px solid #2563eb;
}

/* Animation slide-in */
.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

/* Notification types */
.notification-info {
  border-left: 4px solid #3b82f6;
}

.notification-success {
  border-left: 4px solid #10b981;
}

.notification-warning {
  border-left: 4px solid #f59e0b;
}

.notification-error {
  border-left: 4px solid #ef4444;
}

/* Events page styles */
.tree-node {
  transition: all 0.2s ease;
}

.tree-node:hover {
  background-color: #f8fafc;
}

.tree-node.active {
  background-color: #eff6ff;
  border: 1px solid #dbeafe;
}

.status-active {
  background-color: #dcfce7;
  color: #166534;
}

.status-inactive {
  background-color: #fef2f2;
  color: #991b1b;
}

/* Detail panel styles */
.detail-panel {
  transition: transform 0.3s ease;
}

.detail-panel.closed {
  transform: translateX(100%);
}

/* Mobile responsive adjustments */
@media (max-width: 1024px) {
  .tree-node {
    padding: 0.75rem;
  }

  .tree-node .w-8 {
    width: 1.5rem;
    height: 1.5rem;
  }

  .tree-node .w-6 {
    width: 1.25rem;
    height: 1.25rem;
  }
}
