"""
Service administration pour Notiflair API

Gestion administrative et opérations de maintenance.
"""

from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func
import json
import gzip
import hashlib
import logging

from app.models.user import User
from app.models.notification import Notification
from app.models.sync import SyncSnapshot
from app.models.translation import TranslationVersion
from app.models.reference import Country, Language
from app.schemas.admin import (
    AdminStatsResponse,
    VersionPublishResponse,
    SnapshotGenerateResponse,
    SnapshotListItem,
    CacheClearResponse
)
from app.utils.logging import get_logger

logger = get_logger("admin_service")


class AdminService:
    """Service d'administration système."""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def get_system_stats(self) -> AdminStatsResponse:
        """
        Récupère les statistiques système complètes.
        
        Returns:
            AdminStatsResponse: Statistiques d'administration
        """
        try:
            now = datetime.utcnow()
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            week_start = today_start - timedelta(days=7)
            
            # Statistiques utilisateurs
            total_users = self.db.query(func.count(User.usr_id)).scalar() or 0
            
            active_users_today = self.db.query(func.count(User.usr_id)).filter(
                User.usr_last_sync >= today_start
            ).scalar() or 0
            
            active_users_week = self.db.query(func.count(User.usr_id)).filter(
                User.usr_last_sync >= week_start
            ).scalar() or 0
            
            new_users_today = self.db.query(func.count(User.usr_id)).filter(
                User.usr_created_at >= today_start
            ).scalar() or 0
            
            # Statistiques notifications
            total_notifications = self.db.query(func.count(Notification.ntf_id)).scalar() or 0
            
            active_notifications = self.db.query(func.count(Notification.ntf_id)).filter(
                Notification.ntf_is_active == True
            ).scalar() or 0
            
            # TODO: Calculer total_translations et pending_translations
            total_translations = 0
            pending_translations = 0
            
            # Statistiques synchronisation
            total_snapshots = self.db.query(func.count(SyncSnapshot.sss_id)).scalar() or 0
            
            snapshots_today = self.db.query(func.count(SyncSnapshot.sss_id)).filter(
                SyncSnapshot.sss_created_at >= today_start
            ).scalar() or 0
            
            # TODO: Calculer sync_requests depuis logs
            sync_requests_today = active_users_today
            sync_requests_week = active_users_week
            
            # Statistiques base de données
            # TODO: Récupérer taille DB, nombre de tables, etc.
            database_size_mb = None
            table_count = 0
            index_count = 0
            
            # Statistiques système
            # TODO: Récupérer métriques système
            api_uptime_seconds = 0.0
            memory_usage_mb = None
            cpu_usage_percent = None
            
            # Dernières activités
            last_user = self.db.query(User).order_by(User.usr_created_at.desc()).first()
            last_user_registration = last_user.usr_created_at if last_user else None
            
            # TODO: Récupérer autres dernières activités
            last_notification_update = None
            last_version_published = None
            last_snapshot_generated = None
            
            last_snapshot = self.db.query(SyncSnapshot).order_by(
                SyncSnapshot.sss_created_at.desc()
            ).first()
            if last_snapshot:
                last_snapshot_generated = last_snapshot.sss_created_at
            
            logger.info("Récupération statistiques système")
            
            return AdminStatsResponse(
                total_users=total_users,
                active_users_today=active_users_today,
                active_users_week=active_users_week,
                new_users_today=new_users_today,
                total_notifications=total_notifications,
                active_notifications=active_notifications,
                total_translations=total_translations,
                pending_translations=pending_translations,
                total_snapshots=total_snapshots,
                snapshots_generated_today=snapshots_today,
                sync_requests_today=sync_requests_today,
                sync_requests_week=sync_requests_week,
                database_size_mb=database_size_mb,
                table_count=table_count,
                index_count=index_count,
                api_uptime_seconds=api_uptime_seconds,
                memory_usage_mb=memory_usage_mb,
                cpu_usage_percent=cpu_usage_percent,
                last_user_registration=last_user_registration,
                last_notification_update=last_notification_update,
                last_version_published=last_version_published,
                last_snapshot_generated=last_snapshot_generated
            )
            
        except Exception as e:
            logger.error(f"Erreur récupération stats système: {str(e)}")
            raise
    
    async def publish_translation_version(
        self,
        country_code: str,
        language_code: str,
        force_major: bool = False,
        force_minor: bool = False
    ) -> VersionPublishResponse:
        """
        Publie une nouvelle version pour un pays/langue.
        
        Args:
            country_code: Code pays
            language_code: Code langue
            force_major: Forcer incrément version majeure
            force_minor: Forcer incrément version mineure
            
        Returns:
            VersionPublishResponse: Résultat de la publication
        """
        try:
            # Récupération des IDs pays/langue
            country = self.db.query(Country).filter(
                Country.cty_code == country_code.upper()
            ).first()
            
            language = self.db.query(Language).filter(
                Language.lng_code == language_code.lower()
            ).first()
            
            if not country or not language:
                raise ValueError(f"Pays/langue non supporté: {country_code}/{language_code}")
            
            # Récupération ou création de la version
            version_info = self.db.query(TranslationVersion).filter(
                TranslationVersion.cty_id == country.cty_id,
                TranslationVersion.lng_id == language.lng_id
            ).first()
            
            if not version_info:
                version_info = TranslationVersion(
                    cty_id=country.cty_id,
                    lng_id=language.lng_id,
                    tvr_current_version="v1.0.0",
                    tvr_has_pending_changes=False,
                    tvr_total_items=0
                )
                self.db.add(version_info)
            
            previous_version = version_info.tvr_current_version
            
            # Calcul de la nouvelle version
            new_version = self._increment_version(
                previous_version, force_major, force_minor
            )
            
            # Mise à jour de la version
            version_info.tvr_current_version = new_version
            version_info.tvr_last_published = datetime.utcnow()
            version_info.tvr_has_pending_changes = False
            
            self.db.commit()
            
            # Génération du snapshot
            snapshot_result = await self.generate_snapshot(
                country_code, language_code, new_version, force_regenerate=True
            )
            
            logger.info(
                f"Version publiée: {country_code}/{language_code} "
                f"{previous_version} -> {new_version}"
            )
            
            return VersionPublishResponse(
                previous_version=previous_version,
                new_version=new_version,
                country_code=country_code,
                language_code=language_code,
                published_at=version_info.tvr_last_published,
                snapshot_generated=True,
                snapshot_id=snapshot_result.snapshot_id,
                items_count=snapshot_result.item_count,
                changelog=None
            )
            
        except Exception as e:
            logger.error(f"Erreur publication version: {str(e)}")
            self.db.rollback()
            raise
    
    async def generate_snapshot(
        self,
        country_code: str,
        language_code: str,
        version: Optional[str] = None,
        force_regenerate: bool = False
    ) -> SnapshotGenerateResponse:
        """
        Génère un snapshot pour un pays/langue/version.
        
        Args:
            country_code: Code pays
            language_code: Code langue
            version: Version spécifique
            force_regenerate: Forcer la régénération
            
        Returns:
            SnapshotGenerateResponse: Résultat de la génération
        """
        try:
            start_time = datetime.utcnow()
            
            # Récupération des IDs pays/langue
            country = self.db.query(Country).filter(
                Country.cty_code == country_code.upper()
            ).first()
            
            language = self.db.query(Language).filter(
                Language.lng_code == language_code.lower()
            ).first()
            
            if not country or not language:
                raise ValueError(f"Pays/langue non supporté: {country_code}/{language_code}")
            
            # Version par défaut si non spécifiée
            if not version:
                version_info = self.db.query(TranslationVersion).filter(
                    TranslationVersion.cty_id == country.cty_id,
                    TranslationVersion.lng_id == language.lng_id
                ).first()
                version = version_info.tvr_current_version if version_info else "v1.0.0"
            
            # Vérification si le snapshot existe déjà
            existing_snapshot = self.db.query(SyncSnapshot).filter(
                SyncSnapshot.cty_id == country.cty_id,
                SyncSnapshot.lng_id == language.lng_id,
                SyncSnapshot.sss_version == version
            ).first()
            
            if existing_snapshot and not force_regenerate:
                raise ValueError(f"Snapshot déjà existant pour {country_code}/{language_code} v{version}")
            
            # Génération des données du snapshot
            snapshot_data = await self._generate_snapshot_data(
                country.cty_id, language.lng_id
            )
            
            # Sérialisation et compression
            json_data = json.dumps(snapshot_data, ensure_ascii=False, separators=(',', ':'))
            json_bytes = json_data.encode('utf-8')
            compressed_data = gzip.compress(json_bytes)
            
            # Calcul du checksum
            checksum = hashlib.sha256(compressed_data).hexdigest()
            
            # Suppression de l'ancien snapshot si régénération
            if existing_snapshot:
                self.db.delete(existing_snapshot)
            
            # Création du nouveau snapshot
            snapshot = SyncSnapshot(
                cty_id=country.cty_id,
                lng_id=language.lng_id,
                sss_version=version,
                sss_data=compressed_data,
                sss_checksum=checksum,
                sss_item_count=len(snapshot_data.get('notifications', [])),
                sss_size_bytes=len(json_bytes),
                sss_size_compressed=len(compressed_data),
                sss_created_at=datetime.utcnow()
            )
            
            self.db.add(snapshot)
            self.db.commit()
            self.db.refresh(snapshot)
            
            generation_time = (datetime.utcnow() - start_time).total_seconds()
            
            logger.info(
                f"Snapshot généré: {country_code}/{language_code} v{version} "
                f"size={len(compressed_data)}B items={snapshot.sss_item_count} "
                f"time={generation_time:.2f}s"
            )
            
            return SnapshotGenerateResponse(
                snapshot_id=snapshot.sss_id,
                version=version,
                country_code=country_code,
                language_code=language_code,
                generated_at=snapshot.sss_created_at,
                size_bytes=snapshot.sss_size_bytes,
                size_compressed=snapshot.sss_size_compressed,
                item_count=snapshot.sss_item_count,
                checksum=snapshot.sss_checksum,
                generation_time_seconds=generation_time
            )
            
        except Exception as e:
            logger.error(f"Erreur génération snapshot: {str(e)}")
            self.db.rollback()
            raise
    
    async def list_snapshots(
        self,
        country_code: Optional[str] = None,
        language_code: Optional[str] = None
    ) -> List[SnapshotListItem]:
        """
        Liste tous les snapshots disponibles.
        
        Args:
            country_code: Filtrer par pays
            language_code: Filtrer par langue
            
        Returns:
            List[SnapshotListItem]: Liste des snapshots
        """
        try:
            query = self.db.query(SyncSnapshot).order_by(
                SyncSnapshot.sss_created_at.desc()
            )
            
            # Filtrage par pays
            if country_code:
                country = self.db.query(Country).filter(
                    Country.cty_code == country_code.upper()
                ).first()
                if country:
                    query = query.filter(SyncSnapshot.cty_id == country.cty_id)
            
            # Filtrage par langue
            if language_code:
                language = self.db.query(Language).filter(
                    Language.lng_code == language_code.lower()
                ).first()
                if language:
                    query = query.filter(SyncSnapshot.lng_id == language.lng_id)
            
            snapshots = query.all()
            
            result = []
            for snapshot in snapshots:
                # Récupération des codes pays/langue
                country = self.db.query(Country).filter(
                    Country.cty_id == snapshot.cty_id
                ).first()
                language = self.db.query(Language).filter(
                    Language.lng_id == snapshot.lng_id
                ).first()
                
                item = SnapshotListItem(
                    sss_id=snapshot.sss_id,
                    version=snapshot.sss_version,
                    country_code=country.cty_code if country else "??",
                    language_code=language.lng_code if language else "??",
                    created_at=snapshot.sss_created_at,
                    size_bytes=snapshot.sss_size_bytes,
                    size_compressed=snapshot.sss_size_compressed,
                    item_count=snapshot.sss_item_count,
                    checksum=snapshot.sss_checksum
                )
                result.append(item)
            
            logger.info(f"Liste snapshots: {len(result)} items")
            return result
            
        except Exception as e:
            logger.error(f"Erreur liste snapshots: {str(e)}")
            raise
    
    async def delete_snapshot(self, snapshot_id: int) -> bool:
        """
        Supprime un snapshot spécifique.
        
        Args:
            snapshot_id: ID du snapshot
            
        Returns:
            bool: Succès de la suppression
        """
        try:
            snapshot = self.db.query(SyncSnapshot).filter(
                SyncSnapshot.sss_id == snapshot_id
            ).first()
            
            if not snapshot:
                raise ValueError(f"Snapshot {snapshot_id} non trouvé")
            
            self.db.delete(snapshot)
            self.db.commit()
            
            logger.warning(f"Snapshot supprimé: {snapshot_id}")
            return True
            
        except Exception as e:
            logger.error(f"Erreur suppression snapshot {snapshot_id}: {str(e)}")
            self.db.rollback()
            raise
    
    async def clear_cache(self, cache_type: str = "all") -> CacheClearResponse:
        """
        Vide le cache Redis.
        
        Args:
            cache_type: Type de cache à vider
            
        Returns:
            CacheClearResponse: Résultat du vidage
        """
        try:
            # TODO: Implémenter vidage cache Redis
            keys_deleted = 0
            
            logger.info(f"Cache vidé: {cache_type}")
            
            return CacheClearResponse(
                cache_type=cache_type,
                keys_deleted=keys_deleted,
                success=True,
                message=f"Cache {cache_type} vidé avec succès",
                cleared_at=datetime.utcnow()
            )
            
        except Exception as e:
            logger.error(f"Erreur vidage cache: {str(e)}")
            return CacheClearResponse(
                cache_type=cache_type,
                keys_deleted=0,
                success=False,
                message=f"Erreur: {str(e)}",
                cleared_at=datetime.utcnow()
            )
    
    def _increment_version(
        self, 
        current_version: str, 
        force_major: bool = False, 
        force_minor: bool = False
    ) -> str:
        """
        Incrémente un numéro de version sémantique.
        
        Args:
            current_version: Version actuelle (ex: v1.2.3)
            force_major: Forcer incrément majeur
            force_minor: Forcer incrément mineur
            
        Returns:
            str: Nouvelle version
        """
        try:
            # Extraction des numéros de version
            version_str = current_version.lstrip('v')
            parts = version_str.split('.')
            
            if len(parts) != 3:
                return "v1.0.0"
            
            major, minor, patch = map(int, parts)
            
            if force_major:
                major += 1
                minor = 0
                patch = 0
            elif force_minor:
                minor += 1
                patch = 0
            else:
                patch += 1
            
            return f"v{major}.{minor}.{patch}"
            
        except Exception:
            return "v1.0.0"
    
    async def _generate_snapshot_data(
        self, 
        country_id: int, 
        language_id: int
    ) -> Dict[str, Any]:
        """
        Génère les données d'un snapshot pour un pays/langue.
        
        Args:
            country_id: ID du pays
            language_id: ID de la langue
            
        Returns:
            Dict: Données du snapshot
        """
        try:
            # TODO: Implémenter génération complète des données
            # avec notifications, traductions, catégories, etc.
            
            return {
                "version": "v1.0.0",
                "country": "FR",
                "language": "fr",
                "generated": datetime.utcnow().isoformat(),
                "notifications": [],
                "translations": {},
                "categories": [],
                "keywords": [],
                "metadata": {
                    "total_items": 0,
                    "generation_time": datetime.utcnow().isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"Erreur génération données snapshot: {str(e)}")
            raise
