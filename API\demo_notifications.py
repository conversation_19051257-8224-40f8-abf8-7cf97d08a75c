#!/usr/bin/env python3
"""
Script de démonstration des endpoints de notifications

Ce script démontre l'utilisation des endpoints créés pour consulter
la hiérarchie des notifications et leurs keywords associés.
"""

import json
import requests
from typing import Dict, Any, List


def pretty_print_json(data: Dict[str, Any], title: str = "", max_items: int = 3):
    """Affiche du JSON de manière formatée avec limitation d'éléments."""
    if title:
        print(f"\n{'='*60}")
        print(f"  {title}")
        print(f"{'='*60}")
    
    # Pour les grandes listes, on limite l'affichage
    if isinstance(data, dict) and "hierarchy" in data:
        # Copie pour modification sans affecter l'original
        display_data = data.copy()
        if len(display_data["hierarchy"]) > max_items:
            display_data["hierarchy"] = display_data["hierarchy"][:max_items]
            display_data["_truncated"] = f"... et {len(data['hierarchy']) - max_items} autres types"
    elif isinstance(data, dict) and "notifications" in data:
        display_data = data.copy()
        if len(display_data["notifications"]) > max_items:
            display_data["notifications"] = display_data["notifications"][:max_items]
            display_data["_truncated"] = f"... et {len(data['notifications']) - max_items} autres notifications"
    else:
        display_data = data
    
    print(json.dumps(display_data, indent=2, ensure_ascii=False))


def test_endpoint(url: str, description: str, max_items: int = 3):
    """Teste un endpoint et affiche le résultat."""
    try:
        print(f"\n🔍 Test: {description}")
        print(f"📡 URL: {url}")
        
        response = requests.get(url)
        
        if response.status_code == 200:
            print(f"✅ Statut: {response.status_code} OK")
            data = response.json()
            
            # Affichage des métadonnées importantes
            if "total_types" in data:
                print(f"📊 Statistiques: {data['total_types']} types, {data['total_notifications']} notifications")
            elif "total_notifications" in data:
                print(f"📊 Statistiques: {data['total_notifications']} notifications, {data['total_keywords']} keywords")
            
            pretty_print_json(data, max_items=max_items)
        else:
            print(f"❌ Erreur: {response.status_code}")
            print(f"Message: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Impossible de se connecter à {url}")
        print("💡 Assurez-vous que l'API est démarrée avec: python main.py")
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")


def analyze_hierarchy(data: Dict[str, Any]):
    """Analyse et affiche des statistiques sur la hiérarchie."""
    if "hierarchy" not in data:
        return
    
    print(f"\n📈 Analyse de la hiérarchie:")
    print(f"   Pays: {data['country_code']} | Langue: {data['language_code']}")
    print(f"   Types totaux: {data['total_types']}")
    print(f"   Notifications totales: {data['total_notifications']}")
    print(f"   Notifications actives: {data['active_notifications']}")
    
    # Analyse des niveaux
    levels = {}
    total_notifications = 0
    
    def analyze_level(items: List[Dict], level: int = 0):
        nonlocal total_notifications
        for item in items:
            type_info = item["notification_type"]
            if level not in levels:
                levels[level] = 0
            levels[level] += 1
            
            total_notifications += len(item["notifications"])
            
            if item["children"]:
                analyze_level(item["children"], level + 1)
    
    analyze_level(data["hierarchy"])
    
    print(f"\n   Répartition par niveau:")
    for level, count in sorted(levels.items()):
        level_name = "Racine" if level == 0 else f"Niveau {level}"
        print(f"     {level_name}: {count} types")


def analyze_keywords(data: Dict[str, Any]):
    """Analyse et affiche des statistiques sur les keywords."""
    if "notifications" not in data:
        return
    
    print(f"\n🏷️ Analyse des keywords:")
    print(f"   Pays: {data['country_code']} | Langue: {data['language_code']}")
    print(f"   Notifications: {data['total_notifications']}")
    print(f"   Keywords uniques: {data['total_keywords']}")
    
    # Analyse de la répartition
    notifications_with_keywords = 0
    total_keyword_associations = 0
    keyword_frequency = {}
    
    for notification in data["notifications"]:
        if notification["keywords"]:
            notifications_with_keywords += 1
            total_keyword_associations += len(notification["keywords"])
            
            for keyword in notification["keywords"]:
                term = keyword["kwt_term"]
                keyword_frequency[term] = keyword_frequency.get(term, 0) + 1
    
    print(f"   Notifications avec keywords: {notifications_with_keywords}")
    print(f"   Associations totales: {total_keyword_associations}")
    
    if keyword_frequency:
        avg_keywords = total_keyword_associations / notifications_with_keywords
        print(f"   Moyenne keywords/notification: {avg_keywords:.1f}")
        
        # Top 5 des keywords les plus fréquents
        top_keywords = sorted(keyword_frequency.items(), key=lambda x: x[1], reverse=True)[:5]
        print(f"   Top 5 keywords:")
        for term, count in top_keywords:
            print(f"     '{term}': {count} notifications")


def main():
    """Fonction principale de démonstration."""
    base_url = "http://localhost:8000/api/v1/notifications"
    
    print("🔔 Démonstration des endpoints de notifications Notiflair")
    print("=" * 60)
    
    # Test 1: Hiérarchie complète pour la Belgique
    hierarchy_url = f"{base_url}/hierarchy?country_code=BE&language_code=fr"
    test_endpoint(
        hierarchy_url,
        "Hiérarchie complète des notifications pour la Belgique (français)",
        max_items=2
    )
    
    # Analyse de la hiérarchie
    try:
        response = requests.get(hierarchy_url)
        if response.status_code == 200:
            analyze_hierarchy(response.json())
    except:
        pass
    
    # Test 2: Keywords pour la Belgique
    keywords_url = f"{base_url}/keywords?country_code=BE&language_code=fr"
    test_endpoint(
        keywords_url,
        "Keywords de toutes les notifications pour la Belgique (français)",
        max_items=3
    )
    
    # Analyse des keywords
    try:
        response = requests.get(keywords_url)
        if response.status_code == 200:
            analyze_keywords(response.json())
    except:
        pass
    
    # Test 3: Keywords d'une notification spécifique
    test_endpoint(
        f"{base_url}/keywords?country_code=BE&language_code=fr&notification_id=243",
        "Keywords d'une notification spécifique (ID: 243)"
    )
    
    # Test 4: Hiérarchie avec notifications inactives
    test_endpoint(
        f"{base_url}/hierarchy?country_code=BE&language_code=fr&include_inactive=true",
        "Hiérarchie incluant les notifications inactives",
        max_items=1
    )
    
    # Test 5: Test avec un autre pays
    test_endpoint(
        f"{base_url}/hierarchy?country_code=FR&language_code=fr",
        "Hiérarchie pour la France (français)",
        max_items=2
    )
    
    # Test 6: Test d'erreur - pays inexistant
    test_endpoint(
        f"{base_url}/hierarchy?country_code=XX&language_code=fr",
        "Test avec un pays inexistant (devrait retourner 404)"
    )
    
    print(f"\n{'='*60}")
    print("✅ Démonstration terminée!")
    print("\n💡 Points clés des endpoints:")
    print("   🌳 /hierarchy : Hiérarchie complète SANS keywords")
    print("   🏷️ /keywords : Keywords par notification AVEC traductions")
    print("   🌍 Paramètres obligatoires : country_code + language_code")
    print("   🔄 Système de fallback pour les traductions")
    print("   📊 Métadonnées complètes incluses")
    print("\n💡 Pour explorer l'API interactivement:")
    print("   👉 Ouvrez http://localhost:8000/docs dans votre navigateur")
    print(f"{'='*60}")


if __name__ == "__main__":
    main()
