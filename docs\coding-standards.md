# Standards de Codage - Projet Notiflair

## 🎯 Vue d'ensemble

Ce document définit les conventions de codage pour l'ensemble du projet Notiflair, incluant tous les sous-projets (API, Frontend, Mobile).

## 📝 Conventions de Nommage

### Variables et Fonctions
- **Python (API)** : `snake_case`
  ```python
  user_name = "john"
  def get_user_data():
  ```
- **JavaScript/TypeScript** : `camelCase`
  ```javascript
  const userName = "john";
  function getUserData() {}
  ```

### Classes
- **Python** : `PascalCase`
  ```python
  class UserManager:
  ```
- **JavaScript/TypeScript** : `PascalCase`
  ```javascript
  class UserManager {}
  ```

### Constantes
- **Toutes langues** : `UPPER_SNAKE_CASE`
  ```python
  MAX_RETRY_COUNT = 3
  API_BASE_URL = "https://api.notiflair.com"
  ```

### Fichiers et Dossiers
- **Python** : `snake_case.py`
- **JavaScript/TypeScript** : `kebab-case.js` ou `camelCase.js`
- **Dossiers** : `kebab-case` ou `snake_case`

## 🔧 Formatage du Code

### Python (API)
- **Formateur** : Black
- **Longueur de ligne** : 88 caractères
- **Imports** : isort pour l'organisation
- **Linting** : Pylint + Flake8

### JavaScript/TypeScript
- **Formateur** : Prettier
- **Longueur de ligne** : 80 caractères
- **Point-virgules** : Obligatoires
- **Guillemets** : Doubles guillemets

### Configuration automatique
- Format automatique à la sauvegarde (VSCode)
- Pre-commit hooks pour validation

## 📚 Documentation

### Docstrings Python
```python
def calculate_notification_score(user_id: int, notification_type: str) -> float:
    """
    Calcule le score de pertinence d'une notification pour un utilisateur.
    
    Args:
        user_id: Identifiant unique de l'utilisateur
        notification_type: Type de notification ('email', 'push', 'sms')
    
    Returns:
        Score de pertinence entre 0.0 et 1.0
    
    Raises:
        ValueError: Si le type de notification n'est pas supporté
    """
```

### Commentaires JSDoc
```javascript
/**
 * Calcule le score de pertinence d'une notification
 * @param {number} userId - Identifiant unique de l'utilisateur
 * @param {string} notificationType - Type de notification
 * @returns {Promise<number>} Score de pertinence
 */
async function calculateNotificationScore(userId, notificationType) {}
```

## 🗂️ Structure des Projets

### API (FastAPI)
```
API/
├── app/
│   ├── models/          # Modèles SQLAlchemy
│   ├── routers/         # Routes FastAPI
│   ├── services/        # Logique métier
│   ├── utils/           # Utilitaires
│   └── database.py      # Configuration DB
├── tests/               # Tests unitaires
├── requirements.txt     # Dépendances
└── main.py             # Point d'entrée
```

## 🔄 Git et Commits

### Format des Commits
```
type(scope): description courte

Description détaillée si nécessaire

Fixes #123
```

### Types de Commits
- `feat`: Nouvelle fonctionnalité
- `fix`: Correction de bug
- `docs`: Documentation
- `style`: Formatage, pas de changement de code
- `refactor`: Refactoring
- `test`: Ajout/modification de tests
- `chore`: Maintenance, configuration

### Exemples
```
feat(api): add user notification preferences endpoint
fix(auth): resolve token expiration issue
docs(readme): update installation instructions
```

## 🧪 Tests

### Conventions de Nommage
- **Fichiers de test** : `test_*.py` ou `*.test.js`
- **Fonctions de test** : `test_should_*` ou `should_*`

### Structure des Tests
```python
def test_should_calculate_notification_score_correctly():
    # Given
    user_id = 123
    notification_type = "email"
    
    # When
    score = calculate_notification_score(user_id, notification_type)
    
    # Then
    assert 0.0 <= score <= 1.0
```

## 🔒 Sécurité

### Variables d'Environnement
- Jamais de secrets dans le code
- Utilisation de `.env` files
- Variables en `UPPER_SNAKE_CASE`

### Validation des Données
- Validation stricte des inputs API
- Utilisation de Pydantic pour FastAPI
- Sanitisation des données utilisateur

## 📊 Performance

### Base de Données
- Index sur les colonnes fréquemment requêtées
- Pagination pour les listes
- Lazy loading quand approprié

### API
- Cache pour les données statiques
- Compression des réponses
- Rate limiting

## 🚀 Déploiement

### Environnements
- `development` : Développement local
- `staging` : Tests d'intégration
- `production` : Production

### Configuration
- Variables d'environnement par environnement
- Logs structurés (JSON)
- Monitoring et alertes

---

*Ce document est vivant et doit être mis à jour selon l'évolution du projet.*
