import apiService, { ApiError } from './api.js';

class AuthService {
  constructor() {
    this.currentUser = null;
    this.token = localStorage.getItem('access_token');
    this.tokenExpiry = localStorage.getItem('token_expiry');
    
    // Vérifier si le token est encore valide au démarrage
    if (this.token && this.tokenExpiry) {
      const now = new Date().getTime();
      const expiry = parseInt(this.tokenExpiry);
      
      if (now >= expiry) {
        this.logout();
      } else {
        // Récupérer les informations utilisateur stockées
        const userData = localStorage.getItem('user_data');
        if (userData) {
          try {
            this.currentUser = JSON.parse(userData);
            apiService.setToken(this.token);
          } catch (error) {
            console.error('Erreur lors du parsing des données utilisateur:', error);
            this.logout();
          }
        }
      }
    }
  }

  /**
   * Connexion utilisateur
   * @param {string} username - Nom d'utilisateur ou email
   * @param {string} password - Mot de passe
   * @returns {Promise<Object>} - Données de l'utilisateur connecté
   */
  async login(username, password) {
    try {
      console.log('🔐 Tentative de connexion pour:', username);
      
      const response = await apiService.post('/auth/login', {
        username: username.trim(),
        password: password
      }, { includeAuth: false });

      // Extraire les données de la réponse
      const { access_token, token_type, expires_in, user } = response;

      // Calculer la date d'expiration
      const now = new Date().getTime();
      const expiryTime = now + (expires_in * 1000); // expires_in est en secondes

      // Stocker les données d'authentification
      this.token = access_token;
      this.currentUser = user;
      this.tokenExpiry = expiryTime.toString();

      // Persister dans localStorage
      localStorage.setItem('access_token', access_token);
      localStorage.setItem('token_expiry', expiryTime.toString());
      localStorage.setItem('user_data', JSON.stringify(user));
      localStorage.setItem('isAuthenticated', 'true');

      // Configurer le service API avec le token
      apiService.setToken(access_token);

      console.log('✅ Connexion réussie pour:', user.username);
      return user;

    } catch (error) {
      console.error('❌ Erreur de connexion:', error);
      
      if (error instanceof ApiError) {
        // Erreurs spécifiques de l'API
        switch (error.status) {
          case 401:
            throw new Error('Nom d\'utilisateur ou mot de passe incorrect');
          case 422:
            throw new Error('Données de connexion invalides');
          case 500:
            throw new Error('Erreur serveur. Veuillez réessayer plus tard');
          default:
            throw new Error(error.message || 'Erreur de connexion');
        }
      } else {
        throw new Error('Impossible de se connecter au serveur');
      }
    }
  }

  /**
   * Déconnexion utilisateur
   */
  async logout() {
    try {
      // Appeler l'endpoint de déconnexion si disponible
      if (this.token) {
        try {
          await apiService.post('/auth/logout');
        } catch (error) {
          console.warn('Erreur lors de la déconnexion côté serveur:', error);
          // On continue la déconnexion locale même si le serveur échoue
        }
      }
    } finally {
      // Nettoyer les données locales
      this.currentUser = null;
      this.token = null;
      this.tokenExpiry = null;

      // Nettoyer localStorage
      localStorage.removeItem('access_token');
      localStorage.removeItem('token_expiry');
      localStorage.removeItem('user_data');
      localStorage.removeItem('isAuthenticated');

      // Nettoyer le service API
      apiService.setToken(null);

      console.log('🚪 Déconnexion effectuée');
    }
  }

  /**
   * Récupérer les informations de l'utilisateur connecté
   * @returns {Promise<Object>} - Données de l'utilisateur
   */
  async getCurrentUser() {
    if (!this.token) {
      throw new Error('Aucun utilisateur connecté');
    }

    try {
      const user = await apiService.get('/auth/me');
      this.currentUser = user;
      localStorage.setItem('user_data', JSON.stringify(user));
      return user;
    } catch (error) {
      console.error('Erreur lors de la récupération des données utilisateur:', error);
      
      if (error.status === 401) {
        // Token expiré ou invalide
        this.logout();
        throw new Error('Session expirée. Veuillez vous reconnecter');
      }
      
      throw error;
    }
  }

  /**
   * Vérifier si l'utilisateur est connecté
   * @returns {boolean}
   */
  isAuthenticated() {
    if (!this.token || !this.tokenExpiry) {
      return false;
    }

    const now = new Date().getTime();
    const expiry = parseInt(this.tokenExpiry);
    
    if (now >= expiry) {
      this.logout();
      return false;
    }

    return true;
  }

  /**
   * Obtenir l'utilisateur actuel (sans appel API)
   * @returns {Object|null}
   */
  getUser() {
    return this.currentUser;
  }

  /**
   * Obtenir le token d'accès
   * @returns {string|null}
   */
  getToken() {
    return this.token;
  }

  /**
   * Vérifier si l'utilisateur a un rôle spécifique
   * @param {string} role - Rôle à vérifier
   * @returns {boolean}
   */
  hasRole(role) {
    return this.currentUser?.roles?.includes(role) || false;
  }

  /**
   * Vérifier si l'utilisateur est super admin
   * @returns {boolean}
   */
  isSuperUser() {
    return this.currentUser?.is_superuser || false;
  }

  /**
   * Obtenir le temps restant avant expiration du token (en minutes)
   * @returns {number}
   */
  getTokenTimeRemaining() {
    if (!this.tokenExpiry) return 0;
    
    const now = new Date().getTime();
    const expiry = parseInt(this.tokenExpiry);
    const remaining = expiry - now;
    
    return Math.max(0, Math.floor(remaining / (1000 * 60)));
  }
}

// Instance singleton du service d'authentification
const authService = new AuthService();

export { AuthService, authService };
export default authService;
