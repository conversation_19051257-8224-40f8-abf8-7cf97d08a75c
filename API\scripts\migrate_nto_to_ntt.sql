-- =====================================================
-- Script de migration des traductions NTO vers NTT
-- =====================================================
-- 
-- Ce script migre les données de la table t_notif_type_trans_ovr_nto 
-- vers la table t_notif_type_trans_ntt
--
-- Tables concernées :
-- - Source : t_notif_type_trans_ovr_nto (overrides par pays)
-- - Destination : t_notif_type_trans_ntt (traductions par défaut)
--
-- ATTENTION : Ce script va insérer de nouvelles données dans t_notif_type_trans_ntt
-- Assurez-vous de faire une sauvegarde avant d'exécuter !
--

-- =====================================================
-- 1. ANALYSE DES DONNÉES EXISTANTES
-- =====================================================

-- Vérifier le nombre d'enregistrements dans chaque table
SELECT 'NTO (source)' as table_name, COUNT(*) as count FROM t_notif_type_trans_ovr_nto
UNION ALL
SELECT 'NTT (destination)' as table_name, COUNT(*) as count FROM t_notif_type_trans_ntt;

-- Vérifier les types de notifications concernés
SELECT 
    nto.ntp_id,
    nt.ntp_identifier_key,
    COUNT(*) as nb_overrides
FROM t_notif_type_trans_ovr_nto nto
JOIN t_notif_type nt ON nto.ntp_id = nt.ntp_id
GROUP BY nto.ntp_id, nt.ntp_identifier_key
ORDER BY nb_overrides DESC;

-- Vérifier les pays et langues concernés
SELECT 
    c.cty_code,
    c.cty_name,
    l.lng_code,
    l.lng_name,
    COUNT(*) as nb_traductions
FROM t_notif_type_trans_ovr_nto nto
JOIN t_country c ON nto.cty_id = c.cty_id
JOIN t_language l ON nto.lng_id = l.lng_id
GROUP BY c.cty_code, c.cty_name, l.lng_code, l.lng_name
ORDER BY c.cty_code, l.lng_code;

-- =====================================================
-- 2. VÉRIFICATION DES CONFLITS POTENTIELS
-- =====================================================

-- Vérifier s'il y a des conflits (même ntp_id + lng_id dans les deux tables)
SELECT 
    nto.ntp_id,
    nto.lng_id,
    l.lng_code,
    nt.ntp_identifier_key,
    'CONFLIT POTENTIEL' as status
FROM t_notif_type_trans_ovr_nto nto
JOIN t_notif_type_trans_ntt ntt ON nto.ntp_id = ntt.ntp_id AND nto.lng_id = ntt.lng_id
JOIN t_language l ON nto.lng_id = l.lng_id
JOIN t_notif_type nt ON nto.ntp_id = nt.ntp_id
ORDER BY nto.ntp_id, nto.lng_id;

-- =====================================================
-- 3. MIGRATION DES DONNÉES (INSERTION)
-- =====================================================

-- Insérer les données de NTO vers NTT (uniquement les nouvelles combinaisons)
INSERT INTO t_notif_type_trans_ntt (
    ntp_id,
    lng_id,
    ntt_label,
    ntt_description,
    ntt_created_at,
    ntt_updated_at
)
SELECT 
    nto.ntp_id,
    nto.lng_id,
    nto.nto_label,
    nto.nto_description,
    NOW() as ntt_created_at,
    NOW() as ntt_updated_at
FROM t_notif_type_trans_ovr_nto nto
WHERE NOT EXISTS (
    -- Éviter les doublons : ne pas insérer si la combinaison ntp_id + lng_id existe déjà
    SELECT 1 
    FROM t_notif_type_trans_ntt ntt 
    WHERE ntt.ntp_id = nto.ntp_id 
    AND ntt.lng_id = nto.lng_id
);

-- =====================================================
-- 4. VÉRIFICATION POST-MIGRATION
-- =====================================================

-- Vérifier le nombre d'enregistrements après migration
SELECT 'NTO (source)' as table_name, COUNT(*) as count FROM t_notif_type_trans_ovr_nto
UNION ALL
SELECT 'NTT (après migration)' as table_name, COUNT(*) as count FROM t_notif_type_trans_ntt;

-- Vérifier les nouvelles données insérées
SELECT 
    ntt.ntp_id,
    nt.ntp_identifier_key,
    l.lng_code,
    ntt.ntt_label,
    ntt.ntt_description,
    ntt.ntt_created_at
FROM t_notif_type_trans_ntt ntt
JOIN t_notif_type nt ON ntt.ntp_id = nt.ntp_id
JOIN t_language l ON ntt.lng_id = l.lng_id
WHERE ntt.ntt_created_at >= CURDATE()  -- Données créées aujourd'hui
ORDER BY ntt.ntp_id, l.lng_code;

-- =====================================================
-- 5. MISE À JOUR DES DONNÉES EXISTANTES (OPTIONNEL)
-- =====================================================

-- Si vous voulez mettre à jour les traductions existantes avec les données NTO :
-- ATTENTION : Ceci va écraser les données existantes !

/*
UPDATE t_notif_type_trans_ntt ntt
JOIN t_notif_type_trans_ovr_nto nto ON ntt.ntp_id = nto.ntp_id AND ntt.lng_id = nto.lng_id
SET 
    ntt.ntt_label = nto.nto_label,
    ntt.ntt_description = nto.nto_description,
    ntt.ntt_updated_at = NOW()
WHERE nto.nto_label IS NOT NULL AND nto.nto_label != '';
*/

-- =====================================================
-- 6. NETTOYAGE (OPTIONNEL)
-- =====================================================

-- Si la migration est réussie et que vous voulez supprimer les données NTO :
-- ATTENTION : Ceci va supprimer définitivement les données de la table NTO !

/*
-- Sauvegarder d'abord dans une table temporaire
CREATE TABLE t_notif_type_trans_ovr_nto_backup AS 
SELECT * FROM t_notif_type_trans_ovr_nto;

-- Puis supprimer les données migrées
DELETE FROM t_notif_type_trans_ovr_nto 
WHERE (ntp_id, lng_id) IN (
    SELECT ntp_id, lng_id FROM t_notif_type_trans_ntt
);
*/

-- =====================================================
-- RÉSUMÉ DE LA MIGRATION
-- =====================================================

SELECT 
    'Migration terminée' as status,
    (SELECT COUNT(*) FROM t_notif_type_trans_ntt) as total_ntt,
    (SELECT COUNT(*) FROM t_notif_type_trans_ovr_nto) as total_nto,
    NOW() as timestamp;
