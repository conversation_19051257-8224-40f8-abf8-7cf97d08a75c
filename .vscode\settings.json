{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit", "source.fixAll.eslint": "explicit", "source.fixAll.pylint": "explicit"}, "editor.bracketPairColorization.enabled": true, "editor.guides.bracketPairs": "active", "editor.guides.bracketPairsHorizontal": "active", "python.defaultInterpreterPath": "./API/venv/bin/python", "python.formatting.provider": "black", "python.linting.enabled": true, "python.linting.pylintEnabled": true, "files.associations": {"*.md": "markdown"}, "files.exclude": {"**/__pycache__": true, "**/.pytest_cache": true, "**/node_modules": true, "**/.env": true}, "search.exclude": {"**/node_modules": true, "**/.venv": true, "**/venv": true}}