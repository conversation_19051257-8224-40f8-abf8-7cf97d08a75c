"""
Modèles SQLAlchemy pour l'API Notiflair

Ce module contient tous les modèles de données basés sur la documentation
de la base de données MySQL.
"""

from .reference import Country, Language, CountryLanguage
from .notifications import (
    NotificationType,
    Notification,
    Keyword,
    NotificationTranslation,
    NotificationTranslationOverride,
    NotificationTypeTranslation,
    NotificationTypeTranslationOverride,
    KeywordTranslation
)

__all__ = [
    "Country",
    "Language",
    "CountryLanguage",
    "NotificationType",
    "Notification",
    "Keyword",
    "NotificationTranslation",
    "NotificationTranslationOverride",
    "NotificationTypeTranslation",
    "NotificationTypeTranslationOverride",
    "KeywordTranslation"
]
