"""
Configuration de l'application Notiflair API

Utilise Pydantic Settings pour la gestion des variables d'environnement.
"""

import os
from typing import List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Configuration de l'application avec validation Pydantic."""
    
    # Informations du projet
    PROJECT_NAME: str = "Notiflair API"
    PROJECT_VERSION: str = "1.0.0"
    PROJECT_DESCRIPTION: str = "API de gestion de notifications multilingues"
    
    # Environnement
    ENVIRONMENT: str = Field(default="development", description="Environment (development, staging, production)")
    DEBUG: bool = Field(default=True, description="Mode debug")
    
    # Serveur
    API_HOST: str = Field(default="0.0.0.0", description="Host d'écoute de l'API")
    API_PORT: int = Field(default=8000, description="Port d'écoute de l'API")
    API_V1_STR: str = Field(default="/api/v1", description="Préfixe API v1")
    
    # Base de données MySQL
    DATABASE_HOST: str = Field(default="localhost", description="Host MySQL")
    DATABASE_PORT: int = Field(default=3306, description="Port MySQL")
    DATABASE_NAME: str = Field(default="notiflair", description="Nom de la base de données")
    DATABASE_USER: str = Field(default="root", description="Utilisateur MySQL")
    DATABASE_PASSWORD: str = Field(default="", description="Mot de passe MySQL")
    
    # Pool de connexions
    DATABASE_POOL_SIZE: int = Field(default=5, description="Taille du pool de connexions")
    DATABASE_MAX_OVERFLOW: int = Field(default=10, description="Connexions supplémentaires max")
    DATABASE_POOL_TIMEOUT: int = Field(default=30, description="Timeout connexion (secondes)")
    DATABASE_POOL_RECYCLE: int = Field(default=3600, description="Recyclage connexions (secondes)")
    
    # Sécurité JWT
    SECRET_KEY: str = Field(description="Clé secrète pour JWT")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, description="Durée de vie token accès (minutes)")
    REFRESH_TOKEN_EXPIRE_DAYS: int = Field(default=7, description="Durée de vie refresh token (jours)")
    ALGORITHM: str = Field(default="HS256", description="Algorithme JWT")
    
    # Documentation
    DOCS_ENABLED: bool = Field(default=True, description="Activer la documentation Swagger")
    REDOC_ENABLED: bool = Field(default=True, description="Activer ReDoc")
    
    # Logging
    LOG_LEVEL: str = Field(default="DEBUG", description="Niveau de log")
    LOG_FORMAT: str = Field(default="json", description="Format des logs")
    LOG_FILE_PATH: str = Field(default="logs/notiflair-api.log", description="Chemin du fichier de log")
    LOG_MAX_SIZE_MB: int = Field(default=10, description="Taille max fichier log (MB)")
    LOG_BACKUP_COUNT: int = Field(default=5, description="Nombre de fichiers de sauvegarde")
    STRUCTURED_LOGGING: bool = Field(default=True, description="Logs structurés JSON")
    
    # CORS
    CORS_ORIGINS: List[str] = Field(
        default=[
            "http://localhost:3000",
            "http://localhost:8080",
            "http://localhost:5173",
            "http://localhost:5174",
            "http://localhost:8501",
            "http://127.0.0.1:3000",
            "http://127.0.0.1:8080",
            "http://127.0.0.1:5173",
            "http://127.0.0.1:5174",
            "http://127.0.0.1:8501"
        ],
        description="Origines autorisées pour CORS"
    )
    
    # Redis (optionnel)
    REDIS_ENABLED: bool = Field(default=False, description="Activer Redis")
    REDIS_HOST: str = Field(default="localhost", description="Host Redis")
    REDIS_PORT: int = Field(default=6379, description="Port Redis")
    REDIS_PASSWORD: str = Field(default="", description="Mot de passe Redis")
    REDIS_DB: int = Field(default=0, description="Base Redis")
    
    # Email (optionnel)
    EMAIL_ENABLED: bool = Field(default=False, description="Activer l'email")
    SMTP_HOST: str = Field(default="", description="Host SMTP")
    SMTP_PORT: int = Field(default=587, description="Port SMTP")
    SMTP_USER: str = Field(default="", description="Utilisateur SMTP")
    SMTP_PASSWORD: str = Field(default="", description="Mot de passe SMTP")
    EMAIL_FROM: str = Field(default="<EMAIL>", description="Email expéditeur")
    
    # Avancé
    REQUEST_TIMEOUT: int = Field(default=30, description="Timeout requêtes (secondes)")
    RATE_LIMIT_ENABLED: bool = Field(default=False, description="Activer le rate limiting")
    RATE_LIMIT_REQUESTS: int = Field(default=100, description="Nombre de requêtes max")
    RATE_LIMIT_WINDOW: int = Field(default=60, description="Fenêtre rate limit (secondes)")
    
    # Monitoring
    MONITORING_ENABLED: bool = Field(default=False, description="Activer le monitoring")
    SENTRY_DSN: str = Field(default="", description="DSN Sentry")
    
    # Tests
    TEST_DATABASE_URL: str = Field(default="sqlite:///./test.db", description="URL base de test")

    # Variables supplémentaires du .env (pour compatibilité)
    DATABASE_URL: Optional[str] = Field(default=None, description="URL complète de la base (optionnel)")
    AUTO_RELOAD: Optional[bool] = Field(default=None, description="Auto-reload (optionnel)")
    REDIS_URL: Optional[str] = Field(default=None, description="URL Redis complète (optionnel)")

    @property
    def database_url(self) -> str:
        """Construit l'URL de connexion MySQL."""
        # Si DATABASE_URL est définie dans .env, l'utiliser
        if self.DATABASE_URL:
            return self.DATABASE_URL

        # Sinon, construire à partir des composants
        password_part = f":{self.DATABASE_PASSWORD}" if self.DATABASE_PASSWORD else ""
        return (
            f"mysql+pymysql://{self.DATABASE_USER}{password_part}"
            f"@{self.DATABASE_HOST}:{self.DATABASE_PORT}/{self.DATABASE_NAME}"
        )
    
    @property
    def redis_url(self) -> str:
        """Construit l'URL de connexion Redis."""
        password_part = f":{self.REDIS_PASSWORD}@" if self.REDIS_PASSWORD else ""
        return f"redis://{password_part}{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
    
    @property
    def is_development(self) -> bool:
        """Vérifie si on est en mode développement."""
        return self.ENVIRONMENT.lower() == "development"
    
    @property
    def is_production(self) -> bool:
        """Vérifie si on est en mode production."""
        return self.ENVIRONMENT.lower() == "production"
    
    class Config:
        """Configuration Pydantic."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
        extra = "ignore"  # Ignore les champs supplémentaires


# Instance globale des paramètres
settings = Settings()


def get_settings() -> Settings:
    """Retourne l'instance des paramètres (pour l'injection de dépendance)."""
    return settings
