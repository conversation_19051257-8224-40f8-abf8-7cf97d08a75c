"""
Modèles SQLAlchemy pour les traductions d'application

Tables liées aux traductions de l'interface utilisateur de l'application.
"""

from datetime import datetime
from typing import List, Optional
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import func

from app.database import Base
from app.models.reference import Language, CountryLanguage


class AppTranslation(Base):
    """
    Modèle pour la table t_app_translations_atr
    
    Clés de traduction pour l'interface utilisateur de l'application.
    """
    __tablename__ = 't_app_translations_atr'
    
    # Colonnes
    atr_id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    atr_key: Mapped[str] = mapped_column(String(255), unique=True, nullable=False, index=True)
    atr_context: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    atr_created_at: Mapped[Optional[datetime]] = mapped_column(DateTime, default=func.now())
    atr_updated_at: Mapped[Optional[datetime]] = mapped_column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relations
    values: Mapped[List["AppTranslationValue"]] = relationship(
        "AppTranslationValue",
        back_populates="translation"
    )
    overrides: Mapped[List["AppTranslationOverride"]] = relationship(
        "AppTranslationOverride",
        back_populates="translation"
    )
    
    def __repr__(self) -> str:
        return f"<AppTranslation(atr_id={self.atr_id}, key='{self.atr_key}', context='{self.atr_context}')>"


class AppTranslationValue(Base):
    """
    Modèle pour la table t_atr_translations_atv ⭐ DÉJÀ CORRECT

    Valeurs de traduction par LANGUE uniquement (structure déjà correcte).
    Utilise lng_id pour les traductions universelles par langue.
    """
    __tablename__ = 't_atr_translations_atv'
    
    # Colonnes (clé primaire composite)
    atr_id: Mapped[int] = mapped_column(Integer, ForeignKey('t_app_translations_atr.atr_id'), primary_key=True)
    lng_id: Mapped[int] = mapped_column(Integer, ForeignKey('tr_languages_lng.lng_id'), primary_key=True)
    atv_value: Mapped[str] = mapped_column(Text, nullable=False)
    atv_created_at: Mapped[Optional[datetime]] = mapped_column(DateTime, default=func.now())
    atv_updated_at: Mapped[Optional[datetime]] = mapped_column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relations
    translation: Mapped[AppTranslation] = relationship("AppTranslation", back_populates="values")
    language: Mapped[Language] = relationship("Language", viewonly=True)
    
    def __repr__(self) -> str:
        return f"<AppTranslationValue(atr_id={self.atr_id}, lng_id={self.lng_id}, value='{self.atv_value[:50]}...')>"


class AppTranslationOverride(Base):
    """
    Modèle pour la table t_atv_override_ato ⭐ NOUVEAU

    Overrides des traductions d'app par PAYS-LANGUE (post-correction 2025).
    Référence une clé de traduction (atr_id) + une combinaison pays-langue (ctl_id).
    """
    __tablename__ = 't_atv_override_ato'
    
    # Colonnes
    ato_id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    atr_id: Mapped[int] = mapped_column(Integer, ForeignKey('t_app_translations_atr.atr_id'), nullable=False)
    ctl_id: Mapped[int] = mapped_column(Integer, ForeignKey('tj_country_languages_ctl.ctl_id'), nullable=False)
    ato_value: Mapped[str] = mapped_column(Text, nullable=False)
    ato_created_at: Mapped[Optional[datetime]] = mapped_column(DateTime, default=func.now())
    ato_updated_at: Mapped[Optional[datetime]] = mapped_column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relations
    translation: Mapped[AppTranslation] = relationship("AppTranslation", back_populates="overrides")
    country_language: Mapped[CountryLanguage] = relationship("CountryLanguage", viewonly=True)
    
    def __repr__(self) -> str:
        return f"<AppTranslationOverride(ato_id={self.ato_id}, atr_id={self.atr_id}, ctl_id={self.ctl_id}, value='{self.ato_value[:50]}...')>"
