"""
Endpoints d'authentification pour l'admin Notiflair

Ce module contient les routes pour l'authentification des utilisateurs admin,
la gestion des tokens JWT et les informations utilisateur.
"""

import logging
from datetime import timedel<PERSON>
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import HTTPBearer

from app.config import settings
from app.services.auth import AuthService, get_auth_service, AdminUser
from app.core.security import (
    get_current_active_user,
    get_current_superuser,
    create_access_token
)
from app.schemas.auth import (
    LoginRequest,
    LoginResponse,
    AdminUserResponse,
    AdminUserMe,
    ErrorResponse
)

# Configuration du logger
logger = logging.getLogger(__name__)

# Configuration du router
router = APIRouter(
    prefix="/auth",
    tags=["Authentication"],
    responses={
        401: {"model": ErrorResponse, "description": "Non autorisé"},
        403: {"model": ErrorResponse, "description": "Accès interdit"},
        500: {"model": ErrorResponse, "description": "Erreur serveur"}
    }
)

# Schéma de sécurité pour la documentation
security = HTTPBearer()


@router.post(
    "/login",
    response_model=LoginResponse,
    status_code=status.HTTP_200_OK,
    summary="Connexion admin",
    description="Authentifie un utilisateur admin et retourne un token JWT",
    responses={
        200: {
            "description": "Connexion réussie",
            "model": LoginResponse
        },
        401: {
            "description": "Credentials invalides",
            "model": ErrorResponse,
            "content": {
                "application/json": {
                    "example": {
                        "error": True,
                        "message": "Nom d'utilisateur ou mot de passe incorrect",
                        "status_code": 401,
                        "path": "/api/v1/auth/login"
                    }
                }
            }
        },
        422: {
            "description": "Données de requête invalides",
            "model": ErrorResponse
        }
    }
)
async def login(
    request: Request,
    login_data: LoginRequest,
    auth_service: AuthService = Depends(get_auth_service)
) -> LoginResponse:
    """
    Authentifie un utilisateur admin et retourne un token JWT.
    
    - **username**: Nom d'utilisateur ou adresse email
    - **password**: Mot de passe
    
    Retourne un token JWT valide pour 30 minutes par défaut.
    """
    logger.info(f"Tentative de connexion pour: {login_data.username}")
    
    try:
        # Authentifier l'utilisateur
        user = auth_service.authenticate_user(login_data.username, login_data.password)
        
        if not user:
            logger.warning(f"Échec d'authentification pour: {login_data.username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Nom d'utilisateur ou mot de passe incorrect",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        if not user.is_active:
            logger.warning(f"Tentative de connexion d'un utilisateur inactif: {login_data.username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Compte utilisateur désactivé",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Créer le token d'accès
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.username},
            expires_delta=access_token_expires
        )
        
        # Mettre à jour la dernière connexion
        auth_service.update_last_login(user.username)
        
        # Préparer la réponse utilisateur
        user_response = AdminUserResponse(
            id=user.id,
            username=user.username,
            email=user.email,
            full_name=user.full_name,
            is_active=user.is_active,
            is_superuser=user.is_superuser,
            roles=user.roles,
            created_at=user.created_at,
            last_login=user.last_login
        )
        
        logger.info(f"Connexion réussie pour: {user.username} (ID: {user.id})")
        
        return LoginResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,  # En secondes
            user=user_response
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erreur lors de la connexion: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur interne lors de l'authentification"
        )


@router.get(
    "/me",
    response_model=AdminUserMe,
    status_code=status.HTTP_200_OK,
    summary="Informations utilisateur",
    description="Récupère les informations de l'utilisateur connecté",
    dependencies=[Depends(security)],
    responses={
        200: {
            "description": "Informations utilisateur",
            "model": AdminUserMe
        },
        401: {
            "description": "Token invalide ou expiré",
            "model": ErrorResponse
        }
    }
)
async def get_current_user_info(
    current_user: AdminUser = Depends(get_current_active_user)
) -> AdminUserMe:
    """
    Récupère les informations détaillées de l'utilisateur connecté.
    
    Nécessite un token JWT valide dans l'en-tête Authorization.
    """
    logger.info(f"Récupération des informations pour: {current_user.username}")
    
    # Calculer les permissions basées sur les rôles
    permissions = current_user.roles.copy()
    
    # Ajouter des permissions spéciales pour les super-utilisateurs
    if current_user.is_superuser:
        permissions.extend([
            "admin:all",
            "read:users",
            "write:users",
            "delete:users",
            "read:notifications",
            "write:notifications",
            "delete:notifications"
        ])
    elif "admin" in current_user.roles:
        permissions.extend([
            "read:users",
            "read:notifications",
            "write:notifications"
        ])
    elif "moderator" in current_user.roles:
        permissions.extend([
            "read:notifications",
            "write:notifications"
        ])
    
    # Supprimer les doublons
    permissions = list(set(permissions))
    
    return AdminUserMe(
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        full_name=current_user.full_name,
        is_active=current_user.is_active,
        is_superuser=current_user.is_superuser,
        roles=current_user.roles,
        created_at=current_user.created_at,
        last_login=current_user.last_login,
        permissions=permissions
    )


@router.post(
    "/logout",
    status_code=status.HTTP_200_OK,
    summary="Déconnexion",
    description="Déconnecte l'utilisateur (invalide le token côté client)",
    dependencies=[Depends(security)],
    responses={
        200: {
            "description": "Déconnexion réussie",
            "content": {
                "application/json": {
                    "example": {
                        "message": "Déconnexion réussie",
                        "success": True
                    }
                }
            }
        }
    }
)
async def logout(
    current_user: AdminUser = Depends(get_current_active_user)
) -> dict[str, Any]:
    """
    Déconnecte l'utilisateur.
    
    Note: Avec JWT, la déconnexion est principalement côté client.
    Le token reste techniquement valide jusqu'à son expiration.
    """
    logger.info(f"Déconnexion de: {current_user.username}")
    
    return {
        "message": "Déconnexion réussie",
        "success": True
    }


@router.get(
    "/users",
    response_model=list[AdminUserResponse],
    status_code=status.HTTP_200_OK,
    summary="Liste des utilisateurs admin",
    description="Récupère la liste de tous les utilisateurs admin (super-utilisateurs uniquement)",
    dependencies=[Depends(security)],
    responses={
        200: {
            "description": "Liste des utilisateurs",
            "model": list[AdminUserResponse]
        },
        403: {
            "description": "Privilèges insuffisants",
            "model": ErrorResponse
        }
    }
)
async def get_all_users(
    current_user: AdminUser = Depends(get_current_superuser),
    auth_service: AuthService = Depends(get_auth_service)
) -> list[AdminUserResponse]:
    """
    Récupère la liste de tous les utilisateurs admin.
    
    Accessible uniquement aux super-utilisateurs.
    """
    logger.info(f"Récupération de la liste des utilisateurs par: {current_user.username}")
    
    try:
        users = auth_service.get_all_users()
        
        # Convertir en AdminUserResponse
        return [
            AdminUserResponse(
                id=user.id,
                username=user.username,
                email=user.email,
                full_name=user.full_name,
                is_active=user.is_active,
                is_superuser=user.is_superuser,
                roles=user.roles,
                created_at=user.created_at,
                last_login=user.last_login
            )
            for user in users
        ]
        
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des utilisateurs: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la récupération des utilisateurs"
        )
