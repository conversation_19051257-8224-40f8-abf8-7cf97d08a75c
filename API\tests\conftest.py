"""
Configuration pytest pour les tests Notiflair API

Fixtures communes et configuration de test.
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
import tempfile
import os

from main import app
from app.database import get_db, Base
from app.config import settings


# Base de données de test en mémoire
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """Override de la dépendance de base de données pour les tests."""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


@pytest.fixture(scope="session")
def db_engine():
    """Fixture pour le moteur de base de données de test."""
    return engine


@pytest.fixture(scope="function")
def db_session(db_engine):
    """
    Fixture pour une session de base de données de test.
    
    Crée les tables au début et les supprime à la fin de chaque test.
    """
    # Création des tables
    Base.metadata.create_all(bind=db_engine)
    
    # Création de la session
    session = TestingSessionLocal()
    
    try:
        yield session
    finally:
        session.close()
        # Suppression des tables après le test
        Base.metadata.drop_all(bind=db_engine)


@pytest.fixture(scope="function")
def client(db_session):
    """
    Fixture pour le client de test FastAPI.
    
    Override la dépendance de base de données avec la session de test.
    """
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    # Nettoyage
    app.dependency_overrides.clear()


@pytest.fixture
def sample_device_id():
    """Fixture pour un device_id de test."""
    return "test-device-12345-67890-abcdef"


@pytest.fixture
def sample_user_data():
    """Fixture pour des données utilisateur de test."""
    return {
        "device_id": "test-device-12345-67890-abcdef",
        "email": "<EMAIL>"
    }


@pytest.fixture
def auth_headers(client, sample_user_data):
    """
    Fixture pour les headers d'authentification.
    
    Crée un utilisateur et retourne les headers avec token.
    """
    # Connexion pour obtenir un token
    response = client.post("/api/v1/auth/login", json=sample_user_data)
    assert response.status_code == 200
    
    token_data = response.json()
    token = token_data["access_token"]
    
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def sample_country_data():
    """Fixture pour des données de pays de test."""
    return {
        "cty_code": "FR",
        "cty_name": "France",
        "cty_timezone": "Europe/Paris",
        "cty_default_lng_id": 1
    }


@pytest.fixture
def sample_language_data():
    """Fixture pour des données de langue de test."""
    return {
        "lng_code": "fr",
        "lng_name": "Français"
    }


@pytest.fixture
def sample_notification_data():
    """Fixture pour des données de notification de test."""
    return {
        "identifier_key": "test_notification",
        "is_active": True,
        "rrule": "FREQ=YEARLY;BYMONTH=1;BYMONTHDAY=1",
        "rrule_text": "Chaque 1er janvier",
        "relevance_score": 7.5
    }


@pytest.fixture
def temp_file():
    """Fixture pour un fichier temporaire."""
    fd, path = tempfile.mkstemp()
    try:
        yield path
    finally:
        os.close(fd)
        os.unlink(path)


# Configuration pytest
def pytest_configure(config):
    """Configuration globale pytest."""
    # Désactivation des logs pendant les tests
    import logging
    logging.disable(logging.CRITICAL)


def pytest_unconfigure(config):
    """Nettoyage après les tests."""
    import logging
    logging.disable(logging.NOTSET)
