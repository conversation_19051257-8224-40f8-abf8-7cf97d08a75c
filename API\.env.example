# =============================================================================
# NOTIFLAIR API - CONFIGURATION ENVIRONMENT
# =============================================================================

# 📊 DATABASE CONFIGURATION
# MySQL connection parameters
DATABASE_URL=mysql+pymysql://root:@localhost:3306/notiflair
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_NAME=notiflair
DATABASE_USER=root
DATABASE_PASSWORD=

# Database pool settings
DATABASE_POOL_SIZE=5
DATABASE_MAX_OVERFLOW=10
DATABASE_POOL_TIMEOUT=30
DATABASE_POOL_RECYCLE=3600

# 🔐 SECURITY CONFIGURATION
# JWT Secret key - CHANGE THIS IN PRODUCTION!
SECRET_KEY=notiflair-dev-secret-key-change-in-production-2024-very-long-secure-key

# JWT token expiration
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
ALGORITHM=HS256

# 🌐 SERVER CONFIGURATION
# Environment: development, staging, production
ENVIRONMENT=development
DEBUG=True

# Server binding
API_HOST=0.0.0.0
API_PORT=8000
API_V1_STR=/api/v1

# Auto-reload in development
AUTO_RELOAD=True

# 📚 DOCUMENTATION
# Enable/disable API documentation
DOCS_ENABLED=True
REDOC_ENABLED=True

# 📝 LOGGING CONFIGURATION
# Log level: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=DEBUG
LOG_FORMAT=json

# File logging
LOG_FILE_PATH=logs/notiflair-api.log
LOG_MAX_SIZE_MB=10
LOG_BACKUP_COUNT=5

# Structured logging
STRUCTURED_LOGGING=True

# 🚀 REDIS CONFIGURATION (Optional - Disabled for now)
# Redis for caching and sessions
REDIS_ENABLED=False
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_URL=redis://localhost:6379/0

# 🌍 CORS CONFIGURATION
# Allowed origins for CORS - Development setup
CORS_ORIGINS=["http://localhost:3000","http://localhost:8080","http://localhost:5173","http://localhost:8501"]

# 📧 EMAIL CONFIGURATION (Disabled for now)
# SMTP settings for email notifications
EMAIL_ENABLED=False
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
EMAIL_FROM=<EMAIL>

# 🔧 ADVANCED SETTINGS
# Request timeout
REQUEST_TIMEOUT=30

# Rate limiting (disabled in development)
RATE_LIMIT_ENABLED=False
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Monitoring (disabled in development)
MONITORING_ENABLED=False
SENTRY_DSN=

# 🧪 TESTING
# Test database (uses SQLite by default)
TEST_DATABASE_URL=sqlite:///./test.db
