#!/usr/bin/env python3
"""
Script de test automatique pour l'API Notiflair

Ce script teste les endpoints principaux de l'API pour vérifier
qu'elle fonctionne correctement.

Usage:
    python test_api.py [--host localhost] [--port 8000]
"""

import requests
import json
import sys
import argparse
import time
from typing import Dict, Any, Optional


class NotifairAPITester:
    """Testeur automatique pour l'API Notiflair."""
    
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.access_token: Optional[str] = None
        self.user_id: Optional[int] = None
        
        # Configuration des timeouts
        self.session.timeout = 10
        
        # Headers par défaut
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'Notiflair-API-Tester/1.0'
        })
    
    def log(self, message: str, level: str = "INFO"):
        """Log avec timestamp."""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
    
    def test_endpoint(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Test un endpoint et retourne le résultat."""
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = self.session.request(method, url, **kwargs)
            
            result = {
                'success': response.status_code < 400,
                'status_code': response.status_code,
                'url': url,
                'method': method,
                'response_time': response.elapsed.total_seconds()
            }
            
            try:
                result['data'] = response.json()
            except:
                result['data'] = response.text
            
            return result
            
        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'error': str(e),
                'url': url,
                'method': method
            }
    
    def test_root_endpoint(self) -> bool:
        """Test l'endpoint racine."""
        self.log("🔍 Test de l'endpoint racine...")
        
        result = self.test_endpoint('GET', '/')
        
        if result['success']:
            data = result['data']
            if isinstance(data, dict) and 'message' in data and 'Notiflair' in data['message']:
                self.log("✅ Endpoint racine OK")
                return True
            else:
                self.log("❌ Réponse inattendue de l'endpoint racine", "ERROR")
                return False
        else:
            self.log(f"❌ Erreur endpoint racine: {result.get('error', result.get('status_code'))}", "ERROR")
            return False
    
    def test_health_endpoint(self) -> bool:
        """Test l'endpoint de santé."""
        self.log("🔍 Test de l'endpoint de santé...")
        
        result = self.test_endpoint('GET', '/health')
        
        if result['success']:
            data = result['data']
            if isinstance(data, dict) and data.get('status') in ['healthy', 'unhealthy']:
                status = data['status']
                db_connected = data.get('database', {}).get('connected', False)
                
                if status == 'healthy' and db_connected:
                    self.log("✅ Health check OK - Base de données connectée")
                    return True
                else:
                    self.log(f"⚠️ Health check: {status}, DB: {'OK' if db_connected else 'KO'}", "WARN")
                    return False
            else:
                self.log("❌ Réponse inattendue du health check", "ERROR")
                return False
        else:
            self.log(f"❌ Erreur health check: {result.get('error', result.get('status_code'))}", "ERROR")
            return False
    
    def test_authentication(self) -> bool:
        """Test l'authentification."""
        self.log("🔍 Test de l'authentification...")
        
        # Données de test
        device_id = f"test-device-{int(time.time())}"
        auth_data = {
            "device_id": device_id,
            "email": "<EMAIL>"
        }
        
        result = self.test_endpoint('POST', '/api/v1/auth/login', json=auth_data)
        
        if result['success']:
            data = result['data']
            if isinstance(data, dict) and 'access_token' in data:
                self.access_token = data['access_token']
                self.user_id = data.get('user_id')
                
                # Mise à jour des headers pour les requêtes suivantes
                self.session.headers['Authorization'] = f"Bearer {self.access_token}"
                
                self.log(f"✅ Authentification OK - User ID: {self.user_id}")
                return True
            else:
                self.log("❌ Réponse d'authentification invalide", "ERROR")
                return False
        else:
            self.log(f"❌ Erreur d'authentification: {result.get('error', result.get('status_code'))}", "ERROR")
            return False
    
    def test_token_verification(self) -> bool:
        """Test la vérification du token."""
        if not self.access_token:
            self.log("⚠️ Pas de token pour la vérification", "WARN")
            return False
        
        self.log("🔍 Test de vérification du token...")
        
        result = self.test_endpoint('GET', '/api/v1/auth/verify')
        
        if result['success']:
            data = result['data']
            if isinstance(data, dict) and data.get('valid') is True:
                self.log("✅ Vérification du token OK")
                return True
            else:
                self.log("❌ Token invalide", "ERROR")
                return False
        else:
            self.log(f"❌ Erreur vérification token: {result.get('error', result.get('status_code'))}", "ERROR")
            return False
    
    def test_user_endpoints(self) -> bool:
        """Test les endpoints utilisateur."""
        if not self.access_token:
            self.log("⚠️ Pas de token pour les tests utilisateur", "WARN")
            return False
        
        self.log("🔍 Test des endpoints utilisateur...")
        
        # Test récupération profil
        result = self.test_endpoint('GET', '/api/v1/users/me')
        
        if not result['success']:
            self.log(f"❌ Erreur récupération profil: {result.get('error', result.get('status_code'))}", "ERROR")
            return False
        
        # Test mise à jour profil
        update_data = {"email": "<EMAIL>"}
        result = self.test_endpoint('PUT', '/api/v1/users/me', json=update_data)
        
        if not result['success']:
            self.log(f"❌ Erreur mise à jour profil: {result.get('error', result.get('status_code'))}", "ERROR")
            return False
        
        # Test sync timestamp
        result = self.test_endpoint('POST', '/api/v1/users/sync')
        
        if result['success']:
            self.log("✅ Endpoints utilisateur OK")
            return True
        else:
            self.log(f"❌ Erreur sync timestamp: {result.get('error', result.get('status_code'))}", "ERROR")
            return False
    
    def test_notification_endpoints(self) -> bool:
        """Test les endpoints de notifications."""
        if not self.access_token:
            self.log("⚠️ Pas de token pour les tests notifications", "WARN")
            return False
        
        self.log("🔍 Test des endpoints notifications...")
        
        # Test liste des notifications
        result = self.test_endpoint('GET', '/api/v1/notifications/')
        
        if not result['success']:
            self.log(f"❌ Erreur liste notifications: {result.get('error', result.get('status_code'))}", "ERROR")
            return False
        
        # Test catégories
        result = self.test_endpoint('GET', '/api/v1/notifications/categories/')
        
        if result['success']:
            self.log("✅ Endpoints notifications OK")
            return True
        else:
            self.log(f"❌ Erreur catégories: {result.get('error', result.get('status_code'))}", "ERROR")
            return False
    
    def test_sync_endpoints(self) -> bool:
        """Test les endpoints de synchronisation."""
        if not self.access_token:
            self.log("⚠️ Pas de token pour les tests sync", "WARN")
            return False
        
        self.log("🔍 Test des endpoints synchronisation...")
        
        # Test vérification version
        result = self.test_endpoint('GET', '/api/v1/sync/version?country_code=FR&language_code=fr')
        
        if not result['success']:
            self.log(f"❌ Erreur vérification version: {result.get('error', result.get('status_code'))}", "ERROR")
            return False
        
        # Test statut sync
        result = self.test_endpoint('GET', '/api/v1/sync/status')
        
        if not result['success']:
            self.log(f"❌ Erreur statut sync: {result.get('error', result.get('status_code'))}", "ERROR")
            return False
        
        # Test pays disponibles
        result = self.test_endpoint('GET', '/api/v1/sync/countries')
        
        if not result['success']:
            self.log(f"❌ Erreur pays disponibles: {result.get('error', result.get('status_code'))}", "ERROR")
            return False
        
        # Test langues disponibles
        result = self.test_endpoint('GET', '/api/v1/sync/languages')
        
        if result['success']:
            self.log("✅ Endpoints synchronisation OK")
            return True
        else:
            self.log(f"❌ Erreur langues disponibles: {result.get('error', result.get('status_code'))}", "ERROR")
            return False
    
    def run_all_tests(self) -> bool:
        """Lance tous les tests."""
        self.log("🚀 Démarrage des tests de l'API Notiflair")
        self.log(f"🌐 URL de base: {self.base_url}")
        
        tests = [
            ("Endpoint racine", self.test_root_endpoint),
            ("Health check", self.test_health_endpoint),
            ("Authentification", self.test_authentication),
            ("Vérification token", self.test_token_verification),
            ("Endpoints utilisateur", self.test_user_endpoints),
            ("Endpoints notifications", self.test_notification_endpoints),
            ("Endpoints synchronisation", self.test_sync_endpoints),
        ]
        
        results = []
        
        for test_name, test_func in tests:
            self.log(f"\n📋 Test: {test_name}")
            try:
                success = test_func()
                results.append((test_name, success))
            except Exception as e:
                self.log(f"❌ Erreur inattendue dans {test_name}: {e}", "ERROR")
                results.append((test_name, False))
        
        # Résumé
        self.log("\n" + "="*50)
        self.log("📊 RÉSUMÉ DES TESTS")
        self.log("="*50)
        
        passed = 0
        total = len(results)
        
        for test_name, success in results:
            status = "✅ PASS" if success else "❌ FAIL"
            self.log(f"{status} {test_name}")
            if success:
                passed += 1
        
        self.log(f"\n🎯 Résultat: {passed}/{total} tests réussis")
        
        if passed == total:
            self.log("🎉 Tous les tests sont passés ! L'API fonctionne correctement.")
            return True
        else:
            self.log("⚠️ Certains tests ont échoué. Vérifiez la configuration et les logs.")
            return False


def main():
    """Point d'entrée principal."""
    parser = argparse.ArgumentParser(description="Testeur automatique pour l'API Notiflair")
    parser.add_argument("--host", default="localhost", help="Host de l'API (défaut: localhost)")
    parser.add_argument("--port", type=int, default=8000, help="Port de l'API (défaut: 8000)")
    parser.add_argument("--https", action="store_true", help="Utiliser HTTPS")
    
    args = parser.parse_args()
    
    # Construction de l'URL de base
    protocol = "https" if args.https else "http"
    base_url = f"{protocol}://{args.host}:{args.port}"
    
    # Lancement des tests
    tester = NotifairAPITester(base_url)
    success = tester.run_all_tests()
    
    # Code de sortie
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
