# Dockerfile pour Notiflair API
# Image de production optimisée pour FastAPI

# Stage 1: Builder
FROM python:3.11-slim as builder

# Variables d'environnement pour Python
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Installation des dépendances système pour la compilation
RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    g++ \
    libffi-dev \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# Création de l'utilisateur non-root
RUN groupadd -r notiflair && useradd -r -g notiflair notiflair

# Répertoire de travail
WORKDIR /app

# Copie et installation des dépendances
COPY requirements.txt .
RUN pip install --user --no-cache-dir -r requirements.txt

# Stage 2: Production
FROM python:3.11-slim as production

# Variables d'environnement
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH="/home/<USER>/.local/bin:$PATH" \
    ENVIRONMENT=production

# Installation des dépendances runtime uniquement
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Création de l'utilisateur non-root
RUN groupadd -r notiflair && useradd -r -g notiflair notiflair

# Copie des dépendances Python depuis le builder
COPY --from=builder /root/.local /home/<USER>/.local

# Répertoire de travail
WORKDIR /app

# Copie du code source
COPY --chown=notiflair:notiflair . .

# Création des dossiers nécessaires
RUN mkdir -p logs && chown -R notiflair:notiflair logs

# Changement vers l'utilisateur non-root
USER notiflair

# Exposition du port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Commande par défaut
CMD ["gunicorn", "main:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000"]
