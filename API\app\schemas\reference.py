"""
Schémas Pydantic pour les données de référence

Schémas de validation et sérialisation pour les pays, langues
et autres données de référence.
"""

from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field


class LanguageResponse(BaseModel):
    """
    Schéma de réponse pour une langue
    """
    lng_id: int = Field(..., description="Identifiant unique de la langue")
    lng_code: str = Field(..., description="Code ISO 639-1 de la langue (fr, en, nl...)")
    lng_name: str = Field(..., description="Nom de la langue dans sa propre langue")
    
    class Config:
        from_attributes = True


class CountryResponse(BaseModel):
    """
    Schéma de réponse pour un pays (sans les langues associées)
    """
    cty_id: int = Field(..., description="Identifiant unique du pays")
    cty_code: str = Field(..., description="Code ISO 3166-1 alpha-2 du pays (FR, BE...)")
    cty_name: str = Field(..., description="Nom officiel du pays")
    cty_timezone: str = Field(..., description="Fuseau horaire IANA (Europe/Paris...)")
    cty_default_lng_id: int = Field(..., description="ID de la langue par défaut")
    cty_created_at: datetime = Field(..., description="Date de création")
    cty_updated_at: datetime = Field(..., description="Dernière modification")
    
    # Langue par défaut (relation)
    default_language: LanguageResponse = Field(..., description="Langue par défaut du pays")
    
    class Config:
        from_attributes = True


class CountryWithLanguagesResponse(BaseModel):
    """
    Schéma de réponse pour un pays avec toutes ses langues associées
    """
    cty_id: int = Field(..., description="Identifiant unique du pays")
    cty_code: str = Field(..., description="Code ISO 3166-1 alpha-2 du pays (FR, BE...)")
    cty_name: str = Field(..., description="Nom officiel du pays")
    cty_timezone: str = Field(..., description="Fuseau horaire IANA (Europe/Paris...)")
    cty_default_lng_id: int = Field(..., description="ID de la langue par défaut")
    cty_created_at: datetime = Field(..., description="Date de création")
    cty_updated_at: datetime = Field(..., description="Dernière modification")
    
    # Relations
    default_language: LanguageResponse = Field(..., description="Langue par défaut du pays")
    languages: List[LanguageResponse] = Field(..., description="Toutes les langues disponibles dans ce pays")
    
    class Config:
        from_attributes = True


class CountryLanguageResponse(BaseModel):
    """
    Schéma de réponse pour une combinaison pays-langue ⭐ NOUVEAU POST-MIGRATION

    Représente le pivot central de la nouvelle architecture.
    """
    ctl_id: int = Field(..., description="ID unique de la combinaison pays-langue")
    cty_id: int = Field(..., description="ID du pays")
    lng_id: int = Field(..., description="ID de la langue")

    # Relations (optionnelles selon le contexte)
    country: Optional[CountryResponse] = Field(None, description="Informations du pays")
    language: Optional[LanguageResponse] = Field(None, description="Informations de la langue")

    class Config:
        from_attributes = True


class CountriesListResponse(BaseModel):
    """
    Schéma de réponse pour la liste de tous les pays avec leurs langues
    """
    countries: List[CountryWithLanguagesResponse] = Field(..., description="Liste de tous les pays")
    total_countries: int = Field(..., description="Nombre total de pays")
    total_languages: int = Field(..., description="Nombre total de langues uniques")

    class Config:
        from_attributes = True
