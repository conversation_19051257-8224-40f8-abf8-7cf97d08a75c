"""
Modèles SQLAlchemy pour les tables de référence

Tables de référence (préfixe tr_) contenant les données statiques
comme les pays, langues, types de notifications, etc.
"""

from datetime import datetime
from typing import List, Optional
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Table
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import func

from app.database import Base


# PIVOT CENTRAL : Modèle complet pour les combinaisons pays-langue
class CountryLanguage(Base):
    """
    Modèle pour la table tj_country_languages_ctl

    ⭐ PIVOT CENTRAL de la nouvelle architecture post-migration 2025

    Cette table centralise toutes les combinaisons pays-langue valides
    et fournit un identifiant unique (ctl_id) utilisé dans toutes les
    tables de traduction, simplifiant drastiquement les overrides.
    """
    __tablename__ = 'tj_country_languages_ctl'

    # Colonnes
    ctl_id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    cty_id: Mapped[int] = mapped_column(Integer, ForeignKey('tr_countries_cty.cty_id'), nullable=False)
    lng_id: Mapped[int] = mapped_column(Integer, ForeignKey('tr_languages_lng.lng_id'), nullable=False)

    # Relations
    country: Mapped["Country"] = relationship("Country", back_populates="country_languages")
    language: Mapped["Language"] = relationship("Language", back_populates="country_languages")

    # Relations vers les overrides qui utilisent ce pivot
    # Note: Les traductions utilisent maintenant lng_id directement

    def __repr__(self) -> str:
        return f"<CountryLanguage(ctl_id={self.ctl_id}, cty_id={self.cty_id}, lng_id={self.lng_id})>"


class Language(Base):
    """
    Modèle pour la table tr_languages_lng
    
    Table de référence des langues disponibles dans l'application.
    """
    __tablename__ = 'tr_languages_lng'
    
    # Colonnes
    lng_id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    lng_code: Mapped[str] = mapped_column(String(5), unique=True, nullable=False, index=True)
    lng_name: Mapped[str] = mapped_column(String(50), nullable=False)
    
    # Relations via le pivot central
    country_languages: Mapped[List["CountryLanguage"]] = relationship(
        "CountryLanguage",
        back_populates="language"
    )

    # Relation indirecte vers les pays via le pivot
    countries: Mapped[List["Country"]] = relationship(
        "Country",
        secondary="tj_country_languages_ctl",
        back_populates="languages",
        viewonly=True
    )
    
    # Pays ayant cette langue comme langue par défaut
    default_for_countries: Mapped[List["Country"]] = relationship(
        "Country",
        back_populates="default_language",
        foreign_keys="Country.cty_default_lng_id"
    )
    
    def __repr__(self) -> str:
        return f"<Language(id={self.lng_id}, code='{self.lng_code}', name='{self.lng_name}')>"


class Country(Base):
    """
    Modèle pour la table tr_countries_cty
    
    Table de référence des pays supportés par l'application.
    """
    __tablename__ = 'tr_countries_cty'
    
    # Colonnes
    cty_id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    cty_code: Mapped[str] = mapped_column(String(2), unique=True, nullable=False, index=True)
    cty_name: Mapped[str] = mapped_column(String(100), nullable=False)
    cty_timezone: Mapped[str] = mapped_column(String(50), nullable=False)
    cty_default_lng_id: Mapped[int] = mapped_column(Integer, ForeignKey('tr_languages_lng.lng_id'), nullable=False)
    cty_created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), nullable=False)
    cty_updated_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relations via le pivot central
    country_languages: Mapped[List[CountryLanguage]] = relationship(
        "CountryLanguage",
        back_populates="country"
    )

    # Relation indirecte vers les langues via le pivot
    languages: Mapped[List[Language]] = relationship(
        "Language",
        secondary="tj_country_languages_ctl",
        back_populates="countries",
        viewonly=True
    )
    
    # Langue par défaut
    default_language: Mapped[Language] = relationship(
        "Language",
        back_populates="default_for_countries",
        foreign_keys=[cty_default_lng_id]
    )
    
    def __repr__(self) -> str:
        return f"<Country(id={self.cty_id}, code='{self.cty_code}', name='{self.cty_name}')>"


# Note: La table tj_country_languages_ctl est maintenant un modèle complet (CountryLanguage)
# avec ctl_id comme clé primaire, servant de pivot central pour toutes les traductions.
# Cette architecture post-migration 2025 simplifie drastiquement les overrides.


# Table d'association pour la relation many-to-many entre notifications et keywords
notification_keyword_association = Table(
    'tj_ntf_keywords_nkw',
    Base.metadata,
    Column('ntf_id', Integer, ForeignKey('t_notifications_ntf.ntf_id'), primary_key=True),
    Column('kwd_id', Integer, ForeignKey('tr_keywords_kwd.kwd_id'), primary_key=True)
)
