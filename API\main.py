"""
Point d'entrée principal de l'API Notiflair

Application FastAPI avec configuration complète pour la production.
"""

import time
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

from app.config import settings
from app.database import check_database_connection, get_database_info


# Configuration du logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL.upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Gestionnaire du cycle de vie de l'application.
    
    Gère le démarrage et l'arrêt propre de l'application.
    """
    # Démarrage
    logger.info("🚀 Démarrage de l'API Notiflair...")
    
    # Vérification de la base de données
    if check_database_connection():
        db_info = get_database_info()
        if db_info:
            logger.info(f"✅ Connexion DB OK: {db_info['database']} v{db_info['version']}")
        else:
            logger.info("✅ Connexion DB OK")
    else:
        logger.error("❌ Impossible de se connecter à la base de données")
        # En production, on pourrait lever une exception ici
        if settings.is_production:
            raise RuntimeError("Base de données inaccessible")
    
    logger.info("✅ API Notiflair démarrée avec succès")
    
    yield
    
    # Arrêt
    logger.info("👋 Arrêt de l'API Notiflair...")


# Création de l'application FastAPI
app = FastAPI(
    title=settings.PROJECT_NAME,
    description=settings.PROJECT_DESCRIPTION,
    version=settings.PROJECT_VERSION,
    docs_url="/docs" if settings.DOCS_ENABLED else None,
    redoc_url="/redoc" if settings.REDOC_ENABLED else None,
    openapi_url="/openapi.json" if settings.DOCS_ENABLED else None,
    lifespan=lifespan
)


# Middleware CORS
logger.info(f"🌐 Configuration CORS - Origines autorisées: {settings.CORS_ORIGINS}")
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)


# Middleware de sécurité (hosts de confiance)
if settings.is_production:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*"]  # À configurer selon vos domaines en production
    )


# Middleware de timing des requêtes
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """Ajoute le temps de traitement dans les headers de réponse."""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response


# Gestionnaires d'erreurs globaux
@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """Gestionnaire d'erreurs HTTP personnalisé."""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": exc.detail,
            "status_code": exc.status_code,
            "path": str(request.url.path)
        }
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Gestionnaire d'erreurs de validation personnalisé."""
    return JSONResponse(
        status_code=422,
        content={
            "error": True,
            "message": "Erreur de validation des données",
            "details": exc.errors(),
            "status_code": 422,
            "path": str(request.url.path)
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Gestionnaire d'erreurs générales."""
    logger.error(f"Erreur inattendue: {exc}", exc_info=True)
    
    if settings.is_development:
        # En développement, on montre l'erreur complète
        return JSONResponse(
            status_code=500,
            content={
                "error": True,
                "message": f"Erreur interne: {str(exc)}",
                "type": type(exc).__name__,
                "status_code": 500,
                "path": str(request.url.path)
            }
        )
    else:
        # En production, on cache les détails
        return JSONResponse(
            status_code=500,
            content={
                "error": True,
                "message": "Erreur interne du serveur",
                "status_code": 500,
                "path": str(request.url.path)
            }
        )


# Routes principales
@app.get("/", tags=["Root"])
async def root() -> Dict[str, Any]:
    """
    Endpoint racine de l'API.
    
    Returns:
        dict: Informations de base sur l'API
    """
    return {
        "message": f"🔔 {settings.PROJECT_NAME}",
        "version": settings.PROJECT_VERSION,
        "status": "running",
        "environment": settings.ENVIRONMENT,
        "docs": "/docs" if settings.DOCS_ENABLED else None,
        "api_v1": settings.API_V1_STR
    }


@app.get("/health", tags=["Health"])
async def health_check() -> Dict[str, Any]:
    """
    Endpoint de vérification de santé de l'API.
    
    Returns:
        dict: Statut de santé de l'API et de ses dépendances
    """
    # Vérification de la base de données
    db_connected = check_database_connection()
    db_info = get_database_info() if db_connected else None
    
    # Statut global
    status = "healthy" if db_connected else "unhealthy"
    
    health_data = {
        "status": status,
        "timestamp": time.time(),
        "database": {
            "connected": db_connected,
            "info": db_info
        },
        "environment": settings.ENVIRONMENT,
        "version": settings.PROJECT_VERSION
    }
    
    # Code de statut HTTP selon la santé
    status_code = 200 if status == "healthy" else 503
    
    return JSONResponse(
        status_code=status_code,
        content=health_data
    )


@app.get("/info", tags=["Info"])
async def app_info() -> Dict[str, Any]:
    """
    Informations détaillées sur l'application.
    
    Returns:
        dict: Informations de configuration (non sensibles)
    """
    return {
        "name": settings.PROJECT_NAME,
        "version": settings.PROJECT_VERSION,
        "description": settings.PROJECT_DESCRIPTION,
        "environment": settings.ENVIRONMENT,
        "debug": settings.DEBUG,
        "docs_enabled": settings.DOCS_ENABLED,
        "api_prefix": settings.API_V1_STR,
        "cors_origins": settings.CORS_ORIGINS if settings.is_development else ["***"],
        "features": {
            "redis": settings.REDIS_ENABLED,
            "email": settings.EMAIL_ENABLED,
            "monitoring": settings.MONITORING_ENABLED,
            "rate_limiting": settings.RATE_LIMIT_ENABLED
        }
    }


# Import et inclusion des routers
from app.routers import reference_router, notifications_router
from app.routers.auth import router as auth_router

# Inclusion des routers avec préfixe API v1
app.include_router(reference_router, prefix=settings.API_V1_STR)
app.include_router(notifications_router, prefix=settings.API_V1_STR)
app.include_router(auth_router, prefix=settings.API_V1_STR)

# Routers à ajouter plus tard
# from app.routers import auth, users, notifications, sync
# app.include_router(auth.router, prefix=settings.API_V1_STR, tags=["Authentication"])
# app.include_router(users.router, prefix=settings.API_V1_STR, tags=["Users"])
# app.include_router(notifications.router, prefix=settings.API_V1_STR, tags=["Notifications"])
# app.include_router(sync.router, prefix=settings.API_V1_STR, tags=["Synchronization"])


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "main:app",
        host=settings.API_HOST,
        port=settings.API_PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
