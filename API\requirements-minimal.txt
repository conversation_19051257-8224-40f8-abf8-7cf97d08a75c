# =============================================================================
# NOTIFLAIR API - DEPENDENCIES MINIMALES
# Compatible Python 3.13 - Sans Pandas pour l'instant
# =============================================================================

# 🚀 FastAPI Core - Essentiels pour l'API
fastapi==0.104.1
uvicorn[standard]==0.24.0

# 📊 Database - SQLAlchemy et MySQL
sqlalchemy==2.0.23
pymysql==1.1.0
cryptography==41.0.7

# 🔐 Authentication & Security - JWT
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# 🌐 HTTP & Validation - Pydantic v2
pydantic==2.5.0
pydantic-settings==2.1.0
requests==2.31.0

# 📝 Configuration & Environment
python-dotenv==1.0.0

# 📅 Date & Time
python-dateutil==2.8.2

# 🧪 Testing - Suite pytest
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2

# 📊 Monitoring & Logging
structlog==23.2.0

# 🔧 Utilities
rich==13.7.0

# 📈 Redis - Cache (optionnel)
redis==5.0.1
