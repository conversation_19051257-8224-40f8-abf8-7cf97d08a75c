"""
Configuration de la base de données SQLAlchemy

Gestion des connexions MySQL avec pool de connexions optimisé.
"""

import asyncio
from typing import AsyncGenerator, Optional
from sqlalchemy import create_engine, text, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from sqlalchemy.exc import SQLAlchemyError

from app.config import settings

# Import des modèles pour que SQLAlchemy les connaisse
# Cet import doit être fait ici pour que les modèles soient enregistrés
# dans Base.metadata avant la création des tables
try:
    from app.models.reference import Country, Language, CountryLanguage
    from app.models.notifications import (
        NotificationType, Notification, Keyword,
        NotificationTranslation, NotificationTranslationOverride,
        NotificationTypeTranslation, NotificationTypeTranslationOverride,
        KeywordTranslation
    )
    from app.models.app_translations import (
        AppTranslation, AppTranslationValue, AppTranslationOverride
    )
except ImportError:
    # Les modèles ne sont pas encore créés, ce n'est pas grave
    pass


# Configuration SQLAlchemy
engine = create_engine(
    settings.database_url,
    poolclass=QueuePool,
    pool_size=settings.DATABASE_POOL_SIZE,
    max_overflow=settings.DATABASE_MAX_OVERFLOW,
    pool_timeout=settings.DATABASE_POOL_TIMEOUT,
    pool_recycle=settings.DATABASE_POOL_RECYCLE,
    pool_pre_ping=True,  # Vérification des connexions
    echo=settings.DEBUG,  # Log des requêtes SQL en mode debug
)

# Session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base pour les modèles
Base = declarative_base()

# Métadonnées pour les contraintes
metadata = MetaData()


def get_db() -> Session:
    """
    Générateur de session de base de données.
    
    Yields:
        Session: Session SQLAlchemy
        
    Usage:
        @app.get("/")
        def read_root(db: Session = Depends(get_db)):
            return db.query(User).all()
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def get_db_async() -> AsyncGenerator[Session, None]:
    """
    Générateur de session de base de données asynchrone.
    
    Yields:
        Session: Session SQLAlchemy
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def check_database_connection() -> bool:
    """
    Vérifie la connexion à la base de données.
    
    Returns:
        bool: True si la connexion fonctionne, False sinon
    """
    try:
        with engine.connect() as connection:
            # Test simple de connexion
            result = connection.execute(text("SELECT 1"))
            return result.fetchone() is not None
    except SQLAlchemyError as e:
        print(f"Erreur de connexion à la base de données: {e}")
        return False
    except Exception as e:
        print(f"Erreur inattendue lors de la connexion DB: {e}")
        return False


async def check_database_connection_async() -> bool:
    """
    Vérifie la connexion à la base de données de manière asynchrone.
    
    Returns:
        bool: True si la connexion fonctionne, False sinon
    """
    def _check():
        return check_database_connection()
    
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(None, _check)


def get_database_info() -> Optional[dict]:
    """
    Récupère les informations de la base de données.
    
    Returns:
        dict: Informations sur la base de données ou None si erreur
    """
    try:
        with engine.connect() as connection:
            # Version MySQL
            version_result = connection.execute(text("SELECT VERSION()"))
            version = version_result.fetchone()[0]
            
            # Nom de la base
            db_result = connection.execute(text("SELECT DATABASE()"))
            database = db_result.fetchone()[0]
            
            # Nombre de tables
            tables_result = connection.execute(text("SHOW TABLES"))
            table_count = len(tables_result.fetchall())
            
            return {
                "version": version,
                "database": database,
                "table_count": table_count,
                "engine": "MySQL",
                "driver": "PyMySQL"
            }
    except SQLAlchemyError as e:
        print(f"Erreur lors de la récupération des infos DB: {e}")
        return None
    except Exception as e:
        print(f"Erreur inattendue lors de la récupération des infos DB: {e}")
        return None


def create_tables():
    """
    Crée toutes les tables définies dans les modèles.
    
    Note:
        Cette fonction doit être appelée après l'import de tous les modèles.
    """
    try:
        Base.metadata.create_all(bind=engine)
        print("✅ Tables créées avec succès")
        return True
    except SQLAlchemyError as e:
        print(f"❌ Erreur lors de la création des tables: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur inattendue lors de la création des tables: {e}")
        return False


def drop_tables():
    """
    Supprime toutes les tables (ATTENTION: Destructif!).
    
    Warning:
        Cette fonction supprime TOUTES les données!
        À utiliser uniquement en développement.
    """
    if not settings.is_development:
        raise RuntimeError("drop_tables() ne peut être utilisé qu'en développement!")
    
    try:
        Base.metadata.drop_all(bind=engine)
        print("⚠️ Toutes les tables ont été supprimées")
        return True
    except SQLAlchemyError as e:
        print(f"❌ Erreur lors de la suppression des tables: {e}")
        return False


def reset_database():
    """
    Remet à zéro la base de données (supprime et recrée les tables).
    
    Warning:
        Cette fonction supprime TOUTES les données!
        À utiliser uniquement en développement.
    """
    if not settings.is_development:
        raise RuntimeError("reset_database() ne peut être utilisé qu'en développement!")
    
    print("⚠️ Remise à zéro de la base de données...")
    
    if drop_tables() and create_tables():
        print("✅ Base de données remise à zéro avec succès")
        return True
    else:
        print("❌ Erreur lors de la remise à zéro de la base de données")
        return False


# Test de connexion au démarrage du module
if __name__ == "__main__":
    print("🔍 Test de connexion à la base de données...")
    
    if check_database_connection():
        print("✅ Connexion à la base de données OK")
        
        info = get_database_info()
        if info:
            print(f"📊 Base: {info['database']}")
            print(f"🔧 Version: {info['version']}")
            print(f"📋 Tables: {info['table_count']}")
    else:
        print("❌ Impossible de se connecter à la base de données")
        print("💡 Vérifiez vos paramètres dans le fichier .env")
