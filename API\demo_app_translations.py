#!/usr/bin/env python3
"""
Script de démonstration des endpoints de traductions d'application

Teste les nouveaux endpoints de traductions d'app avec différents paramètres.
"""

import requests
import json
from typing import Dict, Any


def print_section(title: str):
    """Affiche un titre de section"""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print('='*60)


def print_response(response: requests.Response, title: str = ""):
    """Affiche une réponse API formatée"""
    if title:
        print(f"\n📋 {title}")
        print("-" * 40)
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Status: {response.status_code}")
        
        if isinstance(data, dict) and 'translations' in data:
            # Liste de traductions
            print(f"📊 Total: {data.get('total', 0)} traductions")
            print(f"📄 Résultats: {len(data['translations'])}")
            
            for i, translation in enumerate(data['translations'][:3], 1):
                print(f"\n  {i}. {translation['atr_key']}")
                print(f"     Valeurs: {len(translation.get('values', []))} langues")
                print(f"     Overrides: {len(translation.get('overrides', []))}")
                
                # Afficher quelques valeurs
                for value in translation.get('values', [])[:2]:
                    print(f"     - {value['language_code']}: {value['atv_value']}")
        
        elif isinstance(data, dict) and 'atr_key' in data:
            # Traduction unique
            print(f"🔑 Clé: {data['atr_key']}")
            print(f"📝 Contexte: {data.get('atr_context', 'N/A')}")
            print(f"🌍 Valeurs: {len(data.get('values', []))} langues")
            print(f"🎯 Overrides: {len(data.get('overrides', []))}")
            
            # Afficher toutes les valeurs
            for value in data.get('values', []):
                print(f"  - {value['language_code']}: {value['atv_value']}")
            
            # Afficher les overrides s'il y en a
            for override in data.get('overrides', []):
                print(f"  🎯 {override['country_code']}-{override['language_code']}: {override['ato_value']}")
        
        else:
            print(json.dumps(data, indent=2, ensure_ascii=False))
    else:
        print(f"❌ Erreur {response.status_code}: {response.text}")


def test_app_translations():
    """Teste les endpoints de traductions d'application"""
    base_url = "http://localhost:8000/api/v1"
    
    print_section("DÉMONSTRATION DES ENDPOINTS DE TRADUCTIONS D'APPLICATION")
    
    # Test 1: Liste générale
    print_section("1. Liste générale des traductions (5 premiers)")
    response = requests.get(f"{base_url}/app-translations/?limit=5")
    print_response(response, "GET /app-translations/?limit=5")
    
    # Test 2: Filtrage par langue
    print_section("2. Traductions en français uniquement")
    response = requests.get(f"{base_url}/app-translations/?language=fr&limit=5")
    print_response(response, "GET /app-translations/?language=fr&limit=5")
    
    # Test 3: Filtrage par pays et langue
    print_section("3. Traductions pour la Belgique en français")
    response = requests.get(f"{base_url}/app-translations/?country=BE&language=fr&limit=5")
    print_response(response, "GET /app-translations/?country=BE&language=fr&limit=5")
    
    # Test 4: Recherche par clé partielle
    print_section("4. Recherche par clé partielle 'AddEvent'")
    response = requests.get(f"{base_url}/app-translations/?key_filter=AddEvent&limit=5")
    print_response(response, "GET /app-translations/?key_filter=AddEvent&limit=5")
    
    # Test 5: Traduction spécifique
    print_section("5. Traduction spécifique par clé")
    response = requests.get(f"{base_url}/app-translations/AddEvent.active_on")
    print_response(response, "GET /app-translations/AddEvent.active_on")
    
    # Test 6: Traduction spécifique avec filtres
    print_section("6. Traduction spécifique filtrée par langue")
    response = requests.get(f"{base_url}/app-translations/AddEvent.active_on?language=fr")
    print_response(response, "GET /app-translations/AddEvent.active_on?language=fr")
    
    # Test 7: Pagination
    print_section("7. Test de pagination")
    response = requests.get(f"{base_url}/app-translations/?limit=3&offset=5")
    print_response(response, "GET /app-translations/?limit=3&offset=5")
    
    print_section("DÉMONSTRATION TERMINÉE")
    print("✅ Tous les endpoints de traductions d'application ont été testés avec succès !")
    print("\n📚 Pour plus d'informations, consultez la documentation Swagger :")
    print("   http://localhost:8000/docs")


if __name__ == "__main__":
    try:
        test_app_translations()
    except requests.exceptions.ConnectionError:
        print("❌ Erreur: Impossible de se connecter à l'API")
        print("   Assurez-vous que l'API est démarrée sur http://localhost:8000")
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
