"""
Tests pour les endpoints de données de référence

Tests pour les pays, langues et autres données statiques.
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.models.reference import Country, Language, country_language_association


class TestReferenceEndpoints:
    """Tests pour les endpoints de référence."""
    
    def setup_test_data(self, db_session: Session):
        """
        Crée des données de test dans la base.
        
        Args:
            db_session: Session de base de données de test
        """
        # Création des langues
        french = Language(lng_id=1, lng_code="fr", lng_name="Français")
        english = Language(lng_id=2, lng_code="en", lng_name="English")
        dutch = Language(lng_id=3, lng_code="nl", lng_name="Nederlands")
        
        db_session.add_all([french, english, dutch])
        db_session.flush()  # Pour obtenir les IDs
        
        # Création des pays
        france = Country(
            cty_id=1,
            cty_code="FR",
            cty_name="France",
            cty_timezone="Europe/Paris",
            cty_default_lng_id=1
        )
        
        belgium = Country(
            cty_id=2,
            cty_code="BE",
            cty_name="Belgique",
            cty_timezone="Europe/Brussels",
            cty_default_lng_id=1
        )
        
        db_session.add_all([france, belgium])
        db_session.flush()
        
        # Associations pays-langues via les relations SQLAlchemy
        # France: français, anglais
        france.languages = [french, english]

        # Belgique: français, néerlandais, anglais
        belgium.languages = [french, dutch, english]
        
        db_session.commit()
        
        return {
            "countries": [france, belgium],
            "languages": [french, english, dutch]
        }
    
    def test_get_all_countries_empty(self, client: TestClient, db_session: Session):
        """Test de récupération des pays quand la base est vide."""
        response = client.get("/api/v1/reference/countries")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["total_countries"] == 0
        assert data["total_languages"] == 0
        assert data["countries"] == []
    
    def test_get_all_countries_with_data(self, client: TestClient, db_session: Session):
        """Test de récupération de tous les pays avec données."""
        # Création des données de test
        test_data = self.setup_test_data(db_session)
        
        response = client.get("/api/v1/reference/countries")
        
        assert response.status_code == 200
        data = response.json()
        
        # Vérification des métadonnées
        assert data["total_countries"] == 2
        assert data["total_languages"] == 3
        
        # Vérification des pays
        countries = data["countries"]
        assert len(countries) == 2
        
        # Vérification du premier pays (Belgique - ordre alphabétique)
        belgium = countries[0]
        assert belgium["cty_code"] == "BE"
        assert belgium["cty_name"] == "Belgique"
        assert belgium["cty_timezone"] == "Europe/Brussels"
        assert belgium["default_language"]["lng_code"] == "fr"
        assert len(belgium["languages"]) == 3  # fr, nl, en
        
        # Vérification du second pays (France)
        france = countries[1]
        assert france["cty_code"] == "FR"
        assert france["cty_name"] == "France"
        assert france["cty_timezone"] == "Europe/Paris"
        assert france["default_language"]["lng_code"] == "fr"
        assert len(france["languages"]) == 2  # fr, en
    
    def test_get_country_by_code_success(self, client: TestClient, db_session: Session):
        """Test de récupération d'un pays spécifique par code."""
        # Création des données de test
        self.setup_test_data(db_session)
        
        response = client.get("/api/v1/reference/countries/FR")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["cty_code"] == "FR"
        assert data["cty_name"] == "France"
        assert data["cty_timezone"] == "Europe/Paris"
        assert data["default_language"]["lng_code"] == "fr"
        assert len(data["languages"]) == 2
    
    def test_get_country_by_code_lowercase(self, client: TestClient, db_session: Session):
        """Test de récupération d'un pays avec code en minuscules."""
        # Création des données de test
        self.setup_test_data(db_session)
        
        response = client.get("/api/v1/reference/countries/be")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["cty_code"] == "BE"
        assert data["cty_name"] == "Belgique"
    
    def test_get_country_by_code_not_found(self, client: TestClient, db_session: Session):
        """Test de récupération d'un pays inexistant."""
        response = client.get("/api/v1/reference/countries/XX")
        
        assert response.status_code == 404
        data = response.json()
        
        assert "error" in data
        assert "XX" in data["message"]
    
    def test_get_all_languages_empty(self, client: TestClient, db_session: Session):
        """Test de récupération des langues quand la base est vide."""
        response = client.get("/api/v1/reference/languages")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data == []
    
    def test_get_all_languages_with_data(self, client: TestClient, db_session: Session):
        """Test de récupération de toutes les langues avec données."""
        # Création des données de test
        self.setup_test_data(db_session)
        
        response = client.get("/api/v1/reference/languages")
        
        assert response.status_code == 200
        data = response.json()
        
        assert len(data) == 3
        
        # Vérification de l'ordre alphabétique par nom
        language_names = [lang["lng_name"] for lang in data]
        assert language_names == ["English", "Français", "Nederlands"]
        
        # Vérification des codes
        language_codes = [lang["lng_code"] for lang in data]
        assert "fr" in language_codes
        assert "en" in language_codes
        assert "nl" in language_codes
    
    def test_get_reference_stats_empty(self, client: TestClient, db_session: Session):
        """Test des statistiques avec base vide."""
        response = client.get("/api/v1/reference/stats")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["total_countries"] == 0
        assert data["total_languages"] == 0
        assert data["total_country_language_associations"] == 0
        assert data["average_languages_per_country"] == 0
        assert data["country_with_most_languages"] is None
    
    def test_get_reference_stats_with_data(self, client: TestClient, db_session: Session):
        """Test des statistiques avec données."""
        # Création des données de test
        self.setup_test_data(db_session)
        
        response = client.get("/api/v1/reference/stats")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["total_countries"] == 2
        assert data["total_languages"] == 3
        assert data["total_country_language_associations"] == 5  # FR:2 + BE:3
        assert data["average_languages_per_country"] == 2.5  # 5/2
        
        # Le pays avec le plus de langues devrait être la Belgique (3 langues)
        most_languages = data["country_with_most_languages"]
        assert most_languages is not None
        assert most_languages["code"] == "BE"
        assert most_languages["name"] == "Belgique"
        assert most_languages["language_count"] == 3
    
    def test_response_structure_countries(self, client: TestClient, db_session: Session):
        """Test de la structure de réponse pour les pays."""
        # Création des données de test
        self.setup_test_data(db_session)
        
        response = client.get("/api/v1/reference/countries")
        
        assert response.status_code == 200
        data = response.json()
        
        # Vérification de la structure de réponse
        assert "countries" in data
        assert "total_countries" in data
        assert "total_languages" in data
        
        if data["countries"]:
            country = data["countries"][0]
            
            # Champs obligatoires du pays
            required_fields = [
                "cty_id", "cty_code", "cty_name", "cty_timezone",
                "cty_default_lng_id", "cty_created_at", "cty_updated_at"
            ]
            for field in required_fields:
                assert field in country
            
            # Relations
            assert "default_language" in country
            assert "languages" in country
            
            # Structure de la langue par défaut
            default_lang = country["default_language"]
            lang_fields = ["lng_id", "lng_code", "lng_name"]
            for field in lang_fields:
                assert field in default_lang
            
            # Structure des langues
            if country["languages"]:
                lang = country["languages"][0]
                for field in lang_fields:
                    assert field in lang
