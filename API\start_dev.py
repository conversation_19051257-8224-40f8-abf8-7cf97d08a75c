#!/usr/bin/env python3
"""
Script de démarrage rapide pour l'API Notiflair en développement

Ce script vérifie la configuration et démarre l'API avec les bonnes options.

Usage:
    python start_dev.py [--port 8000] [--host 0.0.0.0]
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path


def check_environment():
    """Vérifie que l'environnement est correctement configuré."""
    print("🔍 Vérification de l'environnement...")
    
    # Vérifier Python
    python_version = sys.version_info
    if python_version < (3, 9):
        print(f"❌ Python 3.9+ requis, version actuelle: {python_version.major}.{python_version.minor}")
        return False
    print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # Vérifier le fichier .env
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ Fichier .env manquant")
        print("💡 Copiez .env.example vers .env et configurez vos paramètres")
        return False
    print("✅ Fichier .env trouvé")
    
    # Vérifier les dépendances critiques
    try:
        import fastapi
        import sqlalchemy
        import uvicorn
        print("✅ Dépendances principales installées")
    except ImportError as e:
        print(f"❌ Dépendance manquante: {e}")
        print("💡 Lancez: pip install -r requirements.txt")
        return False
    
    return True


def check_database_connection():
    """Vérifie la connexion à la base de données."""
    print("🔍 Vérification de la connexion base de données...")

    try:
        from app.database import check_database_connection as db_check

        # Test de connexion (fonction synchrone)
        connected = db_check()

        if connected:
            print("✅ Connexion base de données OK")
            return True
        else:
            print("❌ Impossible de se connecter à la base de données")
            print("💡 Vérifiez vos paramètres DATABASE_* dans .env")
            return False

    except Exception as e:
        print(f"❌ Erreur de connexion DB: {e}")
        print("💡 Vérifiez que MySQL est démarré et que la base 'notiflair' existe")
        return False


def create_tables_if_needed():
    """Crée les tables si elles n'existent pas."""
    print("🔍 Vérification des tables...")
    
    try:
        from app.database import engine, Base
        from sqlalchemy import inspect
        
        # Vérifier si les tables existent
        inspector = inspect(engine)
        existing_tables = inspector.get_table_names()
        
        if not existing_tables:
            print("📋 Aucune table trouvée, création des tables...")
            Base.metadata.create_all(bind=engine)
            print("✅ Tables créées avec succès")
        else:
            print(f"✅ {len(existing_tables)} tables trouvées")
            
        return True
        
    except Exception as e:
        print(f"❌ Erreur création tables: {e}")
        return False


def start_api(host: str, port: int, reload: bool = True):
    """Démarre l'API avec uvicorn."""
    print(f"🚀 Démarrage de l'API sur {host}:{port}")
    
    cmd = [
        "uvicorn",
        "main:app",
        "--host", host,
        "--port", str(port),
    ]
    
    if reload:
        cmd.append("--reload")
    
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n👋 Arrêt de l'API")
    except subprocess.CalledProcessError as e:
        print(f"❌ Erreur de démarrage: {e}")
        return False
    
    return True


def main():
    """Point d'entrée principal."""
    parser = argparse.ArgumentParser(description="Démarrage rapide de l'API Notiflair")
    parser.add_argument("--host", default="localhost", help="Host d'écoute (défaut: localhost)")
    parser.add_argument("--port", type=int, default=8000, help="Port d'écoute (défaut: 8000)")
    parser.add_argument("--no-reload", action="store_true", help="Désactiver le rechargement automatique")
    parser.add_argument("--skip-checks", action="store_true", help="Ignorer les vérifications")
    
    args = parser.parse_args()
    
    print("🔔 Notiflair API - Démarrage en développement")
    print("=" * 50)
    
    if not args.skip_checks:
        # Vérifications préalables
        if not check_environment():
            print("\n❌ Échec de la vérification de l'environnement")
            sys.exit(1)
        
        if not check_database_connection():
            print("\n❌ Échec de la connexion à la base de données")
            sys.exit(1)
        
        if not create_tables_if_needed():
            print("\n❌ Échec de la création des tables")
            sys.exit(1)
    
    print("\n✅ Toutes les vérifications sont passées")
    print(f"🌐 L'API sera accessible sur: http://{args.host}:{args.port}")
    print(f"📚 Documentation: http://{args.host}:{args.port}/docs")
    print(f"🏥 Health check: http://{args.host}:{args.port}/health")
    print("\n" + "=" * 50)
    
    # Démarrage de l'API
    reload = not args.no_reload
    success = start_api(args.host, args.port, reload)
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
