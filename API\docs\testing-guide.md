# 🧪 Guide de Test - Notiflair API

## 📋 Vue d'ensemble

Ce guide explique comment tester l'API Notiflair FastAPI étape par étape, depuis l'installation jusqu'aux tests des endpoints.

## 🚀 Prérequis et Installation

### 1. Vérification de l'environnement

```bash
# Vérifier Python
python --version  # Doit être 3.9+

# Vérifier MySQL
mysql --version   # Doit être 8.0+

# Vérifier Redis (optionnel)
redis-cli ping    # Doit retourner PONG
```

### 2. Installation des dépendances

```bash
cd API

# Créer l'environnement virtuel
python -m venv venv

# Activer l'environnement
# Sur Windows:
venv\Scripts\activate
# Sur Linux/Mac:
source venv/bin/activate

# Installer les dépendances
pip install -r requirements.txt
```

### 3. Configuration de la base de données

#### Création de la base MySQL

```sql
-- Se connecter à MySQL en tant qu'admin
mysql -u root -p

-- Créer la base de données
CREATE DATABASE notiflair CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Créer l'utilisateur
CREATE USER 'notiflair_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON notiflair.* TO 'notiflair_user'@'localhost';
FLUSH PRIVILEGES;

-- Vérifier la connexion
USE notiflair;
SHOW TABLES;  -- Doit être vide pour l'instant
```

#### Configuration des variables d'environnement

```bash
# Copier le template
cp .env.example .env

# Éditer .env avec vos paramètres
nano .env
```

**Paramètres minimaux dans `.env` :**

```env
# Base de données
DATABASE_URL=mysql+pymysql://notiflair_user:your_password@localhost:3306/notiflair
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_NAME=notiflair
DATABASE_USER=notiflair_user
DATABASE_PASSWORD=your_password

# Sécurité
SECRET_KEY=your-super-secret-key-change-this-in-production-min-32-chars

# Développement
DEBUG=True
ENVIRONMENT=development
```

## 🏃‍♂️ Démarrage de l'API

### 1. Lancement en mode développement

```bash
# Depuis le dossier API
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Ou avec la tâche VSCode
# Ctrl+Shift+P > "Tasks: Run Task" > "Start FastAPI Dev Server"
```

### 2. Vérification du démarrage

L'API devrait démarrer et afficher :

```
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [xxxxx] using StatReload
INFO:     Started server process [xxxxx]
INFO:     Waiting for application startup.
INFO:     🚀 Démarrage de l'API Notiflair...
INFO:     ✅ Connexion DB OK: notiflair vX.X.X
INFO:     ✅ API Notiflair démarrée avec succès
INFO:     Application startup complete.
```

## 🔍 Tests des Endpoints

### 1. Tests de base (sans authentification)

#### Endpoint racine
```bash
curl http://localhost:8000/
```

**Réponse attendue :**
```json
{
  "message": "🔔 Notiflair API",
  "version": "1.0.0",
  "status": "running",
  "docs": "/docs",
  "api_v1": "/api/v1"
}
```

#### Health check
```bash
curl http://localhost:8000/health
```

**Réponse attendue :**
```json
{
  "status": "healthy",
  "timestamp": **********.0,
  "database": {
    "connected": true,
    "info": {
      "version": "8.0.35",
      "database": "notiflair"
    }
  },
  "environment": "development",
  "version": "1.0.0"
}
```

#### Documentation Swagger
Ouvrir dans le navigateur : http://localhost:8000/docs

Vous devriez voir l'interface Swagger avec tous les endpoints documentés.

### 2. Tests d'authentification

#### Connexion avec device_id
```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "device_id": "test-device-12345-67890-abcdef",
    "email": "<EMAIL>"
  }'
```

**Réponse attendue :**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 1800,
  "user_id": 1,
  "device_id": "test-device-12345-67890-abcdef"
}
```

**Sauvegarder le token pour les tests suivants :**
```bash
export ACCESS_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

#### Vérification du token
```bash
curl -X GET "http://localhost:8000/api/v1/auth/verify" \
  -H "Authorization: Bearer $ACCESS_TOKEN"
```

### 3. Tests des utilisateurs

#### Récupération du profil
```bash
curl -X GET "http://localhost:8000/api/v1/users/me" \
  -H "Authorization: Bearer $ACCESS_TOKEN"
```

#### Mise à jour du profil
```bash
curl -X PUT "http://localhost:8000/api/v1/users/me" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>"
  }'
```

#### Mise à jour de synchronisation
```bash
curl -X POST "http://localhost:8000/api/v1/users/sync" \
  -H "Authorization: Bearer $ACCESS_TOKEN"
```

### 4. Tests des notifications

#### Liste des notifications
```bash
curl -X GET "http://localhost:8000/api/v1/notifications/" \
  -H "Authorization: Bearer $ACCESS_TOKEN"
```

#### Avec filtres
```bash
curl -X GET "http://localhost:8000/api/v1/notifications/?country_code=FR&language_code=fr&page=1&page_size=10" \
  -H "Authorization: Bearer $ACCESS_TOKEN"
```

#### Catégories de notifications
```bash
curl -X GET "http://localhost:8000/api/v1/notifications/categories/" \
  -H "Authorization: Bearer $ACCESS_TOKEN"
```

### 5. Tests de synchronisation

#### Vérification de version
```bash
curl -X GET "http://localhost:8000/api/v1/sync/version?country_code=FR&language_code=fr" \
  -H "Authorization: Bearer $ACCESS_TOKEN"
```

#### Statut de synchronisation
```bash
curl -X GET "http://localhost:8000/api/v1/sync/status" \
  -H "Authorization: Bearer $ACCESS_TOKEN"
```

#### Pays disponibles
```bash
curl -X GET "http://localhost:8000/api/v1/sync/countries" \
  -H "Authorization: Bearer $ACCESS_TOKEN"
```

#### Langues disponibles
```bash
curl -X GET "http://localhost:8000/api/v1/sync/languages" \
  -H "Authorization: Bearer $ACCESS_TOKEN"
```

## 🧪 Tests automatisés

### Lancement des tests unitaires

```bash
# Installer pytest si pas déjà fait
pip install pytest pytest-asyncio pytest-cov

# Lancer tous les tests
pytest

# Avec couverture de code
pytest --cov=app --cov-report=html

# Tests spécifiques
pytest tests/test_auth.py -v
pytest tests/test_users.py -v
pytest tests/test_notifications.py -v
```

### Tests avec différents niveaux de verbosité

```bash
# Mode silencieux
pytest -q

# Mode verbeux
pytest -v

# Mode très verbeux avec détails
pytest -vv

# Arrêter au premier échec
pytest -x

# Lancer seulement les tests qui ont échoué
pytest --lf
```

## 🐛 Résolution des problèmes courants

### Erreur de connexion à la base de données

**Symptôme :** `sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError)`

**Solutions :**
1. Vérifier que MySQL est démarré
2. Vérifier les credentials dans `.env`
3. Vérifier que la base `notiflair` existe
4. Tester la connexion manuellement :
   ```bash
   mysql -u notiflair_user -p notiflair
   ```

### Erreur de module non trouvé

**Symptôme :** `ModuleNotFoundError: No module named 'app'`

**Solutions :**
1. Vérifier que vous êtes dans le bon dossier (`API/`)
2. Vérifier que l'environnement virtuel est activé
3. Réinstaller les dépendances :
   ```bash
   pip install -r requirements.txt
   ```

### Erreur de token JWT

**Symptôme :** `401 Unauthorized` ou `Token invalide`

**Solutions :**
1. Vérifier que `SECRET_KEY` est défini dans `.env`
2. Refaire une authentification pour obtenir un nouveau token
3. Vérifier que le token n'est pas expiré

### Port déjà utilisé

**Symptôme :** `OSError: [Errno 98] Address already in use`

**Solutions :**
1. Changer le port :
   ```bash
   uvicorn main:app --reload --port 8001
   ```
2. Tuer le processus existant :
   ```bash
   lsof -ti:8000 | xargs kill -9
   ```

## ✅ Checklist de validation

- [ ] L'API démarre sans erreur
- [ ] La connexion à la base de données fonctionne
- [ ] L'endpoint `/health` retourne `healthy`
- [ ] La documentation Swagger est accessible
- [ ] L'authentification par device_id fonctionne
- [ ] Les tokens JWT sont générés correctement
- [ ] Les endpoints utilisateurs répondent
- [ ] Les endpoints notifications répondent
- [ ] Les endpoints de synchronisation répondent
- [ ] Les tests unitaires passent
- [ ] Les logs sont générés correctement

## 📊 Monitoring en développement

### Logs en temps réel

```bash
# Suivre les logs de l'API
tail -f logs/notiflair-api.log

# Ou directement dans la console si configuré
```

### Métriques de performance

Surveiller dans les logs :
- Temps de réponse des endpoints (header `X-Process-Time`)
- Erreurs de base de données
- Erreurs d'authentification
- Utilisation mémoire

### Base de données

```sql
-- Vérifier les tables créées
USE notiflair;
SHOW TABLES;

-- Vérifier les utilisateurs créés
SELECT * FROM t_users_usr;

-- Vérifier les logs de connexion
SELECT usr_device_id, usr_created_at, usr_last_sync 
FROM t_users_usr 
ORDER BY usr_created_at DESC;
```
