# 🔔 Notiflair API

API REST FastAPI pour la gestion de notifications multilingues avec synchronisation mobile.

## 🎯 Fonctionnalités

- **Authentification JWT admin** avec username/password et stockage JSON
- **Endpoints de notifications hiérarchiques** avec traductions multilingues
- **Endpoints de keywords** avec système de fallback
- **Endpoints de référence** (pays, langues, types de notifications)
- **Documentation automatique** Swagger/OpenAPI
- **Tests unitaires** complets avec pytest

## 🏗️ Architecture

```
FastAPI + SQLAlchemy + MySQL
├── Authentification JWT admin (credentials JSON temporaires)
├── Modèles SQLAlchemy avec convention ISO SQL:2013
├── Services métier avec logique de fallback
├── Endpoints hiérarchiques pour notifications et keywords
└── Tests automatisés avec couverture de code
```

## 🚀 Démarrage rapide

### 1. Prérequis

- Python 3.11+
- MySQL 8.0+

### 2. Installation

```bash
# Cloner et naviguer
cd API

# Environnement virtuel
python -m venv venv
source venv/bin/activate  # Linux/Mac
# ou
venv\Scripts\activate     # Windows

# Dépendances
pip install -r requirements.txt
```

### 3. Configuration

```bash
# Copier le template
cp .env.example .env

# Éditer avec vos paramètres
nano .env
```

**Configuration minimale dans `.env` :**

```env
# Base de données
DATABASE_URL=mysql+pymysql://user:password@localhost:3306/notiflair

# Sécurité
SECRET_KEY=your-super-secret-key-change-this-in-production

# Développement
DEBUG=True
ENVIRONMENT=development
```

### 4. Base de données

```sql
-- Créer la base MySQL
CREATE DATABASE notiflair CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'notiflair_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON notiflair.* TO 'notiflair_user'@'localhost';
```

### 5. Démarrage

#### Option A : Script automatique (recommandé)

```bash
python start_dev.py
```

#### Option B : Manuel

```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

#### Option C : VSCode

```
Ctrl+Shift+P > "Tasks: Run Task" > "Start FastAPI Dev Server"
```

### 6. Vérification

- **API** : http://localhost:8000
- **Documentation** : http://localhost:8000/docs
- **Health check** : http://localhost:8000/health

### 7. Test des endpoints

#### Authentification admin

```bash
# Connexion admin (mot de passe par défaut : "secret")
curl -X POST "http://localhost:8000/api/v1/auth/login" \
     -H "Content-Type: application/json" \
     -d '{"username": "admin", "password": "secret"}'

# Récupérer les informations utilisateur (avec le token obtenu)
curl -X GET "http://localhost:8000/api/v1/auth/me" \
     -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

#### Endpoints de notifications

```bash
# Hiérarchie des notifications pour la Belgique en français
curl -X GET "http://localhost:8000/api/v1/notifications/hierarchy?country_code=BE&language_code=fr"

# Keywords des notifications
curl -X GET "http://localhost:8000/api/v1/notifications/keywords?country_code=BE&language_code=fr"

# Données de référence
curl -X GET "http://localhost:8000/api/v1/reference/countries"
curl -X GET "http://localhost:8000/api/v1/reference/languages"
```

#### Script de démonstration

```bash
# Démonstration complète des endpoints
python demo_notifications.py
```

## 🧪 Tests

### Tests automatiques de l'API

```bash
# Test complet de l'API
python test_api.py

# Avec paramètres personnalisés
python test_api.py --host localhost --port 8000
```

### Tests unitaires

```bash
# Tous les tests
pytest

# Avec couverture
pytest --cov=app --cov-report=html

# Tests spécifiques
pytest tests/test_auth.py -v
pytest tests/test_reference.py -v
```

### Tests par catégorie

```bash
# Tests d'authentification
pytest -m auth

# Tests de synchronisation
pytest -m sync

# Tests lents (à éviter en développement)
pytest -m "not slow"
```

## 📚 Documentation

### Endpoints principaux

#### Authentification Admin

- `POST /api/v1/auth/login` - Connexion admin (username/password)
- `GET /api/v1/auth/me` - Informations utilisateur connecté
- `POST /api/v1/auth/logout` - Déconnexion
- `GET /api/v1/auth/users` - Liste des utilisateurs (superuser uniquement)

#### Notifications

- `GET /api/v1/notifications/hierarchy` - Hiérarchie complète des notifications
- `GET /api/v1/notifications/keywords` - Keywords des notifications

#### Référence

- `GET /api/v1/reference/countries` - Liste des pays
- `GET /api/v1/reference/languages` - Liste des langues
- `GET /api/v1/reference/notification-types` - Types de notifications

### Documentation détaillée

- [Base de données](./docs/database.md) - Schéma et structure MySQL
- [Endpoints de notifications](./docs/endpoints-notifications.md) - Documentation complète des endpoints
- [Guide de test](./docs/testing-guide.md) - Tests manuels et automatiques

### Authentification Admin

Le système d'authentification admin utilise JWT avec stockage temporaire des credentials dans un fichier JSON.

#### Credentials par défaut

- **Username** : `admin` (superuser)
- **Password** : `secret`
- **Username** : `moderator` (utilisateur normal)
- **Password** : `secret`

#### Fichier de credentials

Les credentials sont stockés dans `data/admin_credentials.json` avec les mots de passe hashés en bcrypt.

#### Migration future

Ce système sera migré vers une table de base de données plus tard. Le fichier JSON est une solution temporaire pour le développement.

#### Sécurité

- Tokens JWT avec expiration (30 minutes par défaut)
- Mots de passe hashés avec bcrypt
- Middleware de protection des routes
- Gestion des rôles et permissions

## 🔧 Développement

### Structure du projet

```
API/
├── main.py                 # Point d'entrée FastAPI
├── app/
│   ├── config.py          # Configuration Pydantic
│   ├── database.py        # SQLAlchemy setup
│   ├── models/            # Modèles SQLAlchemy
│   ├── routers/           # Endpoints FastAPI
│   │   ├── auth.py        # Authentification admin
│   │   ├── notifications.py # Notifications et keywords
│   │   └── reference.py   # Données de référence
│   ├── services/          # Logique métier
│   │   └── auth.py        # Service d'authentification
│   ├── schemas/           # Validation Pydantic
│   │   ├── auth.py        # Schémas d'authentification
│   │   └── notifications.py # Schémas de notifications
│   ├── core/              # Composants centraux
│   │   └── security.py    # Sécurité JWT
│   └── utils/             # Utilitaires
├── data/                  # Données temporaires
│   └── admin_credentials.json # Credentials admin (temporaire)
├── tests/                 # Tests unitaires
├── docs/                  # Documentation
├── requirements.txt       # Dépendances Python
├── .env.example          # Template configuration
├── start_dev.py          # Script de démarrage
├── test_api.py           # Tests automatiques
└── demo_notifications.py # Script de démonstration
```

### Ajout d'un nouvel endpoint

1. **Créer le schéma Pydantic** dans `app/schemas/`
2. **Ajouter la logique métier** dans `app/services/`
3. **Créer l'endpoint** dans `app/routers/`
4. **Écrire les tests** dans `tests/`

### Convention de code

- **Nommage** : snake_case pour Python, camelCase pour JSON
- **Docstrings** : Format Google pour toutes les fonctions
- **Type hints** : Obligatoires pour toutes les fonctions publiques
- **Tests** : Un test par fonctionnalité, nommage `test_*`

## 🐛 Résolution de problèmes

### Erreurs courantes

#### Connexion base de données

```
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError)
```

**Solution :** Vérifier MySQL démarré et credentials dans `.env`

#### Module non trouvé

```
ModuleNotFoundError: No module named 'app'
```

**Solution :** Activer l'environnement virtuel et réinstaller les dépendances

#### Port occupé

```
OSError: [Errno 98] Address already in use
```

**Solution :** Changer le port ou tuer le processus existant

### Logs et debugging

```bash
# Logs en temps réel
tail -f logs/notiflair-api.log

# Debug avec pdb
python -m pdb main.py

# Profiling
python -m cProfile -o profile.stats main.py
```

## 🚢 Déploiement

### Docker

```bash
# Build
docker build -t notiflair-api .

# Run
docker run -p 8000:8000 --env-file .env notiflair-api
```

### Production

```bash
# Avec Gunicorn
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000

# Variables d'environnement production
export ENVIRONMENT=production
export DEBUG=False
export SECRET_KEY=your-production-secret-key
```

## 📊 Monitoring

### Métriques disponibles

- Temps de réponse (header `X-Process-Time`)
- Statut de santé (`/health`)
- Métriques base de données
- Logs structurés JSON

### Surveillance recommandée

- **Uptime** : Endpoint `/health`
- **Performance** : Temps de réponse moyen
- **Erreurs** : Logs niveau ERROR
- **Base de données** : Connexions actives

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/amazing-feature`)
3. Commit les changements (`git commit -m 'Add amazing feature'`)
4. Push la branche (`git push origin feature/amazing-feature`)
5. Ouvrir une Pull Request

### Standards de qualité

- Tests unitaires obligatoires (couverture > 80%)
- Documentation des nouvelles fonctionnalités
- Respect des conventions de nommage
- Validation avec `black`, `isort`, `flake8`

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier [LICENSE](../LICENSE) pour plus de détails.

## 🆘 Support

- **Issues** : Utiliser GitHub Issues
- **Documentation** : Voir le dossier `docs/`
- **API Reference** : http://localhost:8000/docs (quand l'API est démarrée)
