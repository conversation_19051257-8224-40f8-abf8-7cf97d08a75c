[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "notiflair-api"
version = "1.0.0"
description = "API de gestion de notifications multilingues avec synchronisation mobile"
authors = [
    {name = "Notiflair Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.11,<3.13"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Framework :: FastAPI",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Software Development :: Libraries :: Application Frameworks",
]
keywords = ["fastapi", "api", "notifications", "multilingual", "mobile", "sync"]

dependencies = [
    "fastapi>=0.104.0,<0.105.0",
    "uvicorn[standard]>=0.24.0,<0.25.0",
    "sqlalchemy>=2.0.20,<2.1.0",
    "pymysql>=1.1.0,<1.2.0",
    "python-jose[cryptography]>=3.3.0,<3.4.0",
    "pydantic>=2.5.0,<2.6.0",
    "pydantic-settings>=2.1.0,<2.2.0",
    "python-dotenv>=1.0.0,<1.1.0",
    "pandas>=2.1.0,<2.2.0",
    "numpy>=1.25.0,<1.26.0",
    "structlog>=23.2.0,<24.0.0",
    "rich>=13.7.0,<14.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0,<8.0.0",
    "pytest-asyncio>=0.21.0,<0.22.0",
    "pytest-cov>=4.1.0,<5.0.0",
    "pytest-mock>=3.12.0,<4.0.0",
    "httpx>=0.25.0,<0.26.0",
    "black>=23.11.0,<24.0.0",
    "isort>=5.12.0,<6.0.0",
    "flake8>=6.1.0,<7.0.0",
    "mypy>=1.7.0,<2.0.0",
]
redis = [
    "redis>=5.0.0,<6.0.0",
    "hiredis>=2.2.0,<3.0.0",
]
monitoring = [
    "sentry-sdk[fastapi]>=1.38.0,<2.0.0",
    "prometheus-client>=0.19.0,<1.0.0",
]

[project.urls]
Homepage = "https://github.com/notiflair/notiflair"
Documentation = "https://docs.notiflair.com"
Repository = "https://github.com/notiflair/notiflair.git"
"Bug Tracker" = "https://github.com/notiflair/notiflair/issues"

[project.scripts]
notiflair-api = "main:app"

# Configuration des outils de développement

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app"]
known_third_party = ["fastapi", "sqlalchemy", "pydantic"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "pymysql.*",
    "jose.*",
    "passlib.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "-ra",
    "--strict-markers",
    "--strict-config",
    "--cov=app",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--cov-fail-under=80",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "auth: marks tests as authentication tests",
    "sync: marks tests as synchronization tests",
    "notifications: marks tests as notification tests",
    "admin: marks tests as admin tests",
    "database: marks tests as database tests",
]

[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/venv/*",
    "*/.venv/*",
    "*/env/*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
