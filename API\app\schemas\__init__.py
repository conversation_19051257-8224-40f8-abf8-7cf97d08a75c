"""
Schémas Pydantic pour la sérialisation des données

Ce module contient tous les schémas de validation et sérialisation
pour les endpoints de l'API.
"""

from .reference import CountryResponse, LanguageResponse, CountryWithLanguagesResponse
from .notifications import (
    NotificationTypeResponse,
    NotificationResponse,
    NotificationHierarchyResponse,
    NotificationsHierarchyListResponse,
    KeywordTranslationResponse,
    NotificationKeywordsResponse,
    CountryLanguageKeywordsResponse
)

__all__ = [
    "CountryResponse",
    "LanguageResponse",
    "CountryWithLanguagesResponse",
    "NotificationTypeResponse",
    "NotificationResponse",
    "NotificationHierarchyResponse",
    "NotificationsHierarchyListResponse",
    "KeywordTranslationResponse",
    "NotificationKeywordsResponse",
    "CountryLanguageKeywordsResponse"
]
