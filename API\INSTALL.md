# 🚀 Installation Notiflair API

## 🎯 Prérequis

### Python 3.11 (Recommandé)
**Pourquoi Python 3.11 ?**
- ✅ **Stabilité** : Version LTS largement adoptée
- ✅ **Performance** : 10-60% plus rapide que Python 3.10
- ✅ **Compatibilité** : Support complet de toutes nos dépendances
- ✅ **Production** : Utilisé par la majorité des entreprises

### Installation Python 3.11

#### Windows
```bash
# Option 1: Depuis python.org
# Télécharger: https://www.python.org/downloads/release/python-3119/

# Option 2: Avec Chocolatey
choco install python311

# Option 3: Avec winget
winget install Python.Python.3.11
```

#### macOS
```bash
# Avec Homebrew
brew install python@3.11

# Avec pyenv
pyenv install 3.11.9
pyenv local 3.11.9
```

#### Linux (Ubuntu/Debian)
```bash
# Ajouter le PPA deadsnakes
sudo add-apt-repository ppa:deadsnakes/ppa
sudo apt update
sudo apt install python3.11 python3.11-venv python3.11-dev
```

## 🔧 Installation de l'API

### 1. Vérification Python
```bash
python --version
# Doit afficher: Python 3.11.x
```

### 2. Environnement virtuel
```bash
cd API

# Créer l'environnement avec Python 3.11
python -m venv venv

# Activer l'environnement
# Windows:
venv\Scripts\activate
# Linux/macOS:
source venv/bin/activate

# Vérifier la version dans l'environnement
python --version
```

### 3. Installation des dépendances
```bash
# Mise à jour pip
python -m pip install --upgrade pip

# Installation des dépendances principales
pip install -r requirements.txt

# Installation des dépendances de développement (optionnel)
pip install -e ".[dev]"

# Installation Redis (optionnel)
pip install -e ".[redis]"
```

### 4. Configuration
```bash
# Copier le template
cp .env.example .env

# Éditer avec vos paramètres
# Windows:
notepad .env
# Linux/macOS:
nano .env
```

### 5. Base de données
```sql
-- Se connecter à MySQL
mysql -u root -p

-- Créer la base (si pas déjà fait)
CREATE DATABASE notiflair CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 6. Test de l'installation
```bash
# Test automatique
python start_dev.py

# Ou test manuel
python -c "import fastapi, sqlalchemy, pydantic; print('✅ Toutes les dépendances sont installées')"
```

## 🐛 Résolution de problèmes

### Erreur: "No module named 'fastapi'"
```bash
# Vérifier que l'environnement virtuel est activé
which python  # Linux/macOS
where python   # Windows

# Réinstaller les dépendances
pip install --force-reinstall -r requirements.txt
```

### Erreur: Compilation pandas/numpy
```bash
# Si vous avez Python 3.13, downgrader vers 3.11
# Ou installer les wheels pré-compilés
pip install --only-binary=all pandas numpy
```

### Erreur: MySQL connection
```bash
# Vérifier MySQL
mysql --version
mysql -u root -p -e "SHOW DATABASES;"

# Vérifier les paramètres dans .env
cat .env | grep DATABASE
```

### Erreur: Port déjà utilisé
```bash
# Windows
netstat -ano | findstr :8000
taskkill /PID <PID> /F

# Linux/macOS
lsof -ti:8000 | xargs kill -9
```

## 📦 Installation alternative avec Poetry

Si vous préférez Poetry (gestionnaire de dépendances moderne) :

```bash
# Installer Poetry
curl -sSL https://install.python-poetry.org | python3 -

# Installer les dépendances
poetry install

# Activer l'environnement
poetry shell

# Lancer l'API
poetry run python start_dev.py
```

## 🔄 Mise à jour

```bash
# Activer l'environnement
source venv/bin/activate  # ou venv\Scripts\activate

# Mettre à jour les dépendances
pip install --upgrade -r requirements.txt

# Vérifier les conflits
pip check
```

## ✅ Validation de l'installation

### Tests automatiques
```bash
# Test complet de l'API
python test_api.py

# Tests unitaires
pytest

# Vérification du code
black --check .
isort --check-only .
flake8 .
mypy app/
```

### Tests manuels
1. **API** : http://localhost:8000
2. **Documentation** : http://localhost:8000/docs
3. **Health check** : http://localhost:8000/health

## 🚀 Démarrage rapide

Une fois l'installation terminée :

```bash
# Démarrage automatique (recommandé)
python start_dev.py

# Ou démarrage manuel
uvicorn main:app --reload
```

## 📋 Checklist d'installation

- [ ] Python 3.11 installé et vérifié
- [ ] Environnement virtuel créé et activé
- [ ] Dépendances installées sans erreur
- [ ] Fichier .env configuré
- [ ] Base de données MySQL accessible
- [ ] API démarre sans erreur
- [ ] Tests automatiques passent
- [ ] Documentation accessible

## 🆘 Support

Si vous rencontrez des problèmes :

1. **Vérifiez les logs** : `logs/notiflair-api.log`
2. **Consultez la documentation** : `docs/`
3. **Lancez les tests** : `python test_api.py`
4. **Vérifiez la configuration** : `.env`

## 🔄 Compatibilité Admin Streamlit

Cette installation Python 3.11 est **parfaitement compatible** avec l'interface Admin Streamlit qui utilisera le même environnement Python.
