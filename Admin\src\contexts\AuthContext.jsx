import { createContext, useContext, useEffect, useState } from 'react';
import authService from '../services/auth.js';

// Créer le contexte d'authentification
const AuthContext = createContext(null);

// Hook personnalisé pour utiliser le contexte d'authentification
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth doit être utilisé dans un AuthProvider');
  }
  return context;
};

// Provider du contexte d'authentification
export const AuthProvider = ({ children }) => {
  // Mode développement - contourne l'authentification si pas de backend
  const DEV_MODE = import.meta.env.DEV && window.location.hostname === 'localhost';

  const [user, setUser] = useState(DEV_MODE ? { username: 'admin', full_name: 'Admin Dev', email: '<EMAIL>' } : null);
  const [isAuthenticated, setIsAuthenticated] = useState(DEV_MODE);
  const [isLoading, setIsLoading] = useState(!DEV_MODE);
  const [error, setError] = useState(null);

  // Vérifier l'authentification au chargement
  useEffect(() => {
    const checkAuth = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // En mode développement, on est déjà authentifié
        if (DEV_MODE) {
          setIsLoading(false);
          return;
        }

        if (authService.isAuthenticated()) {
          const currentUser = authService.getUser();
          
          if (currentUser) {
            setUser(currentUser);
            setIsAuthenticated(true);
          } else {
            // Essayer de récupérer les données utilisateur depuis l'API
            try {
              const userData = await authService.getCurrentUser();
              setUser(userData);
              setIsAuthenticated(true);
            } catch (error) {
              console.warn('Impossible de récupérer les données utilisateur:', error);
              await logout();
            }
          }
        } else {
          setUser(null);
          setIsAuthenticated(false);
        }
      } catch (error) {
        console.error('Erreur lors de la vérification de l\'authentification:', error);
        setError('Erreur de vérification de l\'authentification');
        setUser(null);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Fonction de connexion
  const login = async (username, password) => {
    try {
      setIsLoading(true);
      setError(null);

      const userData = await authService.login(username, password);
      
      setUser(userData);
      setIsAuthenticated(true);
      
      return userData;
    } catch (error) {
      setError(error.message);
      setUser(null);
      setIsAuthenticated(false);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction de déconnexion
  const logout = async () => {
    try {
      setIsLoading(true);
      await authService.logout();
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error);
    } finally {
      setUser(null);
      setIsAuthenticated(false);
      setError(null);
      setIsLoading(false);
    }
  };

  // Fonction pour rafraîchir les données utilisateur
  const refreshUser = async () => {
    try {
      if (!isAuthenticated) return null;
      
      const userData = await authService.getCurrentUser();
      setUser(userData);
      return userData;
    } catch (error) {
      console.error('Erreur lors du rafraîchissement des données utilisateur:', error);
      if (error.message.includes('Session expirée')) {
        await logout();
      }
      throw error;
    }
  };

  // Fonction pour vérifier si l'utilisateur a un rôle spécifique
  const hasRole = (role) => {
    return authService.hasRole(role);
  };

  // Fonction pour vérifier si l'utilisateur est super admin
  const isSuperUser = () => {
    return authService.isSuperUser();
  };

  // Fonction pour obtenir le token
  const getToken = () => {
    return authService.getToken();
  };

  // Fonction pour obtenir le temps restant du token
  const getTokenTimeRemaining = () => {
    return authService.getTokenTimeRemaining();
  };

  // Valeurs du contexte
  const value = {
    // État
    user,
    isAuthenticated,
    isLoading,
    error,
    
    // Actions
    login,
    logout,
    refreshUser,
    
    // Utilitaires
    hasRole,
    isSuperUser,
    getToken,
    getTokenTimeRemaining,
    
    // Méthodes pour nettoyer les erreurs
    clearError: () => setError(null),
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
