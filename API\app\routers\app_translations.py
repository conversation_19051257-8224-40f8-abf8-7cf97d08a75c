"""
Router pour les traductions d'application

Endpoints pour consulter les traductions de l'interface utilisateur.
"""

import logging
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session, selectinload, joinedload
from sqlalchemy import func, and_, or_

from app.database import get_db
from app.models.reference import Country, Language, CountryLanguage
from app.models.app_translations import (
    AppTranslation, AppTranslationValue, AppTranslationOverride
)
from app.schemas.app_translations import (
    AppTranslationResponse,
    AppTranslationValueResponse,
    AppTranslationOverrideResponse,
    AppTranslationWithValuesResponse,
    AppTranslationsListResponse
)

router = APIRouter(prefix="/app-translations", tags=["App Translations"])
logger = logging.getLogger(__name__)


@router.get("/", response_model=AppTranslationsListResponse)
async def get_app_translations(
    language: Optional[str] = Query(None, description="Code langue (ex: 'fr')"),
    country: Optional[str] = Query(None, description="Code pays (ex: 'FR')"),
    key_filter: Optional[str] = Query(None, description="Filtre sur les clés (recherche partielle)"),
    limit: int = Query(100, ge=1, le=1000, description="Nombre max de résultats"),
    offset: int = Query(0, ge=0, description="Décalage pour pagination"),
    db: Session = Depends(get_db)
):
    """
    Récupère les traductions d'application avec fallback automatique
    
    Logique de fallback :
    1. Override spécifique (pays-langue) → ctl_id
    2. Traduction langue → lng_id  
    3. Clé technique → key
    """
    try:
        # Construction de la requête de base
        query = db.query(AppTranslation)
        
        # Filtre par clé si spécifié
        if key_filter:
            query = query.filter(AppTranslation.atr_key.contains(key_filter))
        
        # Pagination
        total = query.count()
        translations = query.offset(offset).limit(limit).all()
        
        # Pour chaque traduction, récupérer les valeurs et overrides
        result_translations = []
        
        for translation in translations:
            # Récupérer les valeurs par langue
            values_query = db.query(
                AppTranslationValue,
                Language.lng_code
            ).join(
                Language, AppTranslationValue.lng_id == Language.lng_id
            ).filter(
                AppTranslationValue.atr_id == translation.atr_id
            )
            
            # Filtrer par langue si spécifié
            if language:
                values_query = values_query.filter(Language.lng_code == language)
            
            values_data = values_query.all()
            
            # Récupérer les overrides par pays-langue
            overrides_query = db.query(
                AppTranslationOverride,
                Country.cty_code,
                Language.lng_code
            ).join(
                CountryLanguage, AppTranslationOverride.ctl_id == CountryLanguage.ctl_id
            ).join(
                Country, CountryLanguage.cty_id == Country.cty_id
            ).join(
                Language, CountryLanguage.lng_id == Language.lng_id
            ).filter(
                AppTranslationOverride.atr_id == translation.atr_id
            )
            
            # Filtrer par pays et langue si spécifié
            if country:
                overrides_query = overrides_query.filter(Country.cty_code == country)
            if language:
                overrides_query = overrides_query.filter(Language.lng_code == language)
            
            overrides_data = overrides_query.all()
            
            # Construire les réponses
            values = [
                AppTranslationValueResponse(
                    atr_id=value.atr_id,
                    lng_id=value.lng_id,
                    language_code=lng_code,
                    atv_value=value.atv_value,
                    translation_source="default"
                )
                for value, lng_code in values_data
            ]
            
            overrides = [
                AppTranslationOverrideResponse(
                    ato_id=override.ato_id,
                    atr_id=override.atr_id,
                    ctl_id=override.ctl_id,
                    country_code=cty_code,
                    language_code=lng_code,
                    ato_value=override.ato_value
                )
                for override, cty_code, lng_code in overrides_data
            ]
            
            result_translations.append(
                AppTranslationWithValuesResponse(
                    atr_id=translation.atr_id,
                    atr_key=translation.atr_key,
                    atr_context=translation.atr_context,
                    values=values,
                    overrides=overrides
                )
            )
        
        return AppTranslationsListResponse(
            translations=result_translations,
            total=total
        )
        
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des traductions d'app: {e}")
        raise HTTPException(status_code=500, detail="Erreur interne du serveur")


@router.get("/{key}", response_model=AppTranslationWithValuesResponse)
async def get_app_translation_by_key(
    key: str = Path(..., description="Clé de traduction"),
    language: Optional[str] = Query(None, description="Code langue (ex: 'fr')"),
    country: Optional[str] = Query(None, description="Code pays (ex: 'FR')"),
    db: Session = Depends(get_db)
):
    """
    Récupère une traduction d'application spécifique par sa clé
    """
    try:
        # Récupérer la traduction
        translation = db.query(AppTranslation).filter(
            AppTranslation.atr_key == key
        ).first()
        
        if not translation:
            raise HTTPException(status_code=404, detail=f"Clé de traduction '{key}' non trouvée")
        
        # Récupérer les valeurs et overrides (même logique que l'endpoint liste)
        values_query = db.query(
            AppTranslationValue,
            Language.lng_code
        ).join(
            Language, AppTranslationValue.lng_id == Language.lng_id
        ).filter(
            AppTranslationValue.atr_id == translation.atr_id
        )
        
        if language:
            values_query = values_query.filter(Language.lng_code == language)
        
        values_data = values_query.all()
        
        overrides_query = db.query(
            AppTranslationOverride,
            Country.cty_code,
            Language.lng_code
        ).join(
            CountryLanguage, AppTranslationOverride.ctl_id == CountryLanguage.ctl_id
        ).join(
            Country, CountryLanguage.cty_id == Country.cty_id
        ).join(
            Language, CountryLanguage.lng_id == Language.lng_id
        ).filter(
            AppTranslationOverride.atr_id == translation.atr_id
        )
        
        if country:
            overrides_query = overrides_query.filter(Country.cty_code == country)
        if language:
            overrides_query = overrides_query.filter(Language.lng_code == language)
        
        overrides_data = overrides_query.all()
        
        # Construire la réponse
        values = [
            AppTranslationValueResponse(
                atr_id=value.atr_id,
                lng_id=value.lng_id,
                language_code=lng_code,
                atv_value=value.atv_value,
                translation_source="default"
            )
            for value, lng_code in values_data
        ]
        
        overrides = [
            AppTranslationOverrideResponse(
                ato_id=override.ato_id,
                atr_id=override.atr_id,
                ctl_id=override.ctl_id,
                country_code=cty_code,
                language_code=lng_code,
                ato_value=override.ato_value
            )
            for override, cty_code, lng_code in overrides_data
        ]
        
        return AppTranslationWithValuesResponse(
            atr_id=translation.atr_id,
            atr_key=translation.atr_key,
            atr_context=translation.atr_context,
            values=values,
            overrides=overrides
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erreur lors de la récupération de la traduction '{key}': {e}")
        raise HTTPException(status_code=500, detail="Erreur interne du serveur")
