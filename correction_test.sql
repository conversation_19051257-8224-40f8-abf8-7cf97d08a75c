-- Script de test pour la correction de la structure des traductions
-- Test sur table t_notif_translations_ntr_test

-- 1. <PERSON><PERSON>ter colonne lng_id
ALTER TABLE t_notif_translations_ntr_test 
ADD COLUMN lng_id INT AFTER ctl_id;

-- 2. <PERSON><PERSON><PERSON> les données avec ctl_id vers lng_id
UPDATE t_notif_translations_ntr_test ntr 
JOIN tj_country_languages_ctl ctl ON ntr.ctl_id = ctl.ctl_id 
SET ntr.lng_id = ctl.lng_id 
WHERE ntr.ctl_id IS NOT NULL;

-- 3. Vérifier les résultats
SELECT 
    'Avec ctl_id' as type,
    COUNT(*) as count,
    COUNT(DISTINCT lng_id) as distinct_lng
FROM t_notif_translations_ntr_test 
WHERE ctl_id IS NOT NULL

UNION ALL

SELECT 
    'Avec ctl_id NULL' as type,
    COUNT(*) as count,
    COUNT(DISTINCT lng_id) as distinct_lng
FROM t_notif_translations_ntr_test 
WHERE ctl_id IS NULL;

-- 4. Vérifier qu'aucune donnée n'est perdue
SELECT 
    'Total original' as check_type,
    COUNT(*) as count
FROM t_notif_translations_ntr

UNION ALL

SELECT 
    'Total test' as check_type,
    COUNT(*) as count
FROM t_notif_translations_ntr_test;
