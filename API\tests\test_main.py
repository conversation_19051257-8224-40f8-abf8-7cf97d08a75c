"""
Tests pour l'application principale Notiflair API

Tests des endpoints de base et de la configuration.
"""

import pytest
from fastapi.testclient import TestClient


class TestMainEndpoints:
    """Tests des endpoints principaux."""
    
    def test_root_endpoint(self, client: TestClient):
        """Test de l'endpoint racine."""
        response = client.get("/")
        
        assert response.status_code == 200
        data = response.json()
        
        # Vérification de la structure de réponse
        assert "message" in data
        assert "version" in data
        assert "status" in data
        assert "docs" in data
        assert "api_v1" in data
        
        assert "Notiflair API" in data["message"]
        assert data["status"] == "running"
        assert data["version"] == "1.0.0"
        assert data["api_v1"] == "/api/v1"
    
    def test_health_check_endpoint(self, client: TestClient):
        """Test de l'endpoint de santé."""
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        
        # Vérification de la structure de réponse
        assert "status" in data
        assert "timestamp" in data
        assert "database" in data
        assert "environment" in data
        assert "version" in data
        
        # Le statut doit être healthy ou unhealthy
        assert data["status"] in ["healthy", "unhealthy"]
        
        # Vérification des informations de base de données
        assert "connected" in data["database"]
        assert "info" in data["database"]
        assert isinstance(data["database"]["connected"], bool)
        
        assert data["version"] == "1.0.0"
    
    def test_docs_endpoint_availability(self, client: TestClient):
        """Test de disponibilité de la documentation."""
        # Test de l'endpoint docs (si activé)
        response = client.get("/docs")
        
        # Peut être 200 (docs activées) ou 404 (docs désactivées)
        assert response.status_code in [200, 404]
        
        if response.status_code == 200:
            # Vérification que c'est bien du HTML
            assert "text/html" in response.headers.get("content-type", "")
    
    def test_redoc_endpoint_availability(self, client: TestClient):
        """Test de disponibilité de ReDoc."""
        response = client.get("/redoc")
        
        # Peut être 200 (redoc activé) ou 404 (redoc désactivé)
        assert response.status_code in [200, 404]
        
        if response.status_code == 200:
            assert "text/html" in response.headers.get("content-type", "")
    
    def test_openapi_json_endpoint(self, client: TestClient):
        """Test de l'endpoint OpenAPI JSON."""
        response = client.get("/api/v1/openapi.json")
        
        # Peut être 200 (docs activées) ou 404 (docs désactivées)
        assert response.status_code in [200, 404]
        
        if response.status_code == 200:
            data = response.json()
            
            # Vérification de la structure OpenAPI
            assert "openapi" in data
            assert "info" in data
            assert "paths" in data
            
            assert data["info"]["title"] == "Notiflair API"
            assert data["info"]["version"] == "1.0.0"


class TestAPIStructure:
    """Tests de la structure de l'API."""
    
    def test_api_v1_prefix(self, client: TestClient):
        """Test que tous les endpoints utilisent le préfixe /api/v1."""
        # Test de quelques endpoints connus
        endpoints_to_test = [
            "/api/v1/auth/login",
            "/api/v1/users/me",
            "/api/v1/notifications/",
            "/api/v1/sync/version"
        ]
        
        for endpoint in endpoints_to_test:
            # On teste juste que l'endpoint existe (pas forcément qu'il fonctionne sans auth)
            response = client.get(endpoint)
            
            # Ne doit pas être 404 (endpoint non trouvé)
            # Peut être 401, 403, 422, etc. selon l'endpoint
            assert response.status_code != 404
    
    def test_cors_headers(self, client: TestClient):
        """Test de la présence des headers CORS."""
        response = client.options("/")
        
        # Vérification des headers CORS
        headers = response.headers
        
        # Note: Les headers CORS exacts dépendent de la configuration
        # On vérifie juste que la requête OPTIONS est gérée
        assert response.status_code in [200, 405]  # 405 = Method Not Allowed est acceptable
    
    def test_process_time_header(self, client: TestClient):
        """Test de la présence du header de temps de traitement."""
        response = client.get("/")
        
        assert response.status_code == 200
        
        # Vérification du header X-Process-Time
        assert "X-Process-Time" in response.headers
        
        # Le temps doit être un nombre positif
        process_time = float(response.headers["X-Process-Time"])
        assert process_time >= 0


class TestErrorHandling:
    """Tests de gestion d'erreurs."""
    
    def test_404_endpoint(self, client: TestClient):
        """Test d'endpoint inexistant."""
        response = client.get("/nonexistent-endpoint")
        
        assert response.status_code == 404
        
        # Vérification du format de réponse d'erreur
        data = response.json()
        assert "detail" in data or "message" in data
    
    def test_method_not_allowed(self, client: TestClient):
        """Test de méthode HTTP non autorisée."""
        # POST sur l'endpoint racine qui n'accepte que GET
        response = client.post("/")
        
        assert response.status_code == 405  # Method Not Allowed
    
    def test_invalid_json_payload(self, client: TestClient):
        """Test avec payload JSON invalide."""
        # Tentative d'envoi de JSON malformé
        response = client.post(
            "/api/v1/auth/login",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 422  # Unprocessable Entity
    
    def test_missing_required_fields(self, client: TestClient):
        """Test avec champs requis manquants."""
        # Login sans device_id
        response = client.post("/api/v1/auth/login", json={})
        
        assert response.status_code == 422
        
        data = response.json()
        assert "detail" in data
        
        # Vérification que l'erreur mentionne le champ manquant
        error_details = str(data["detail"])
        assert "device_id" in error_details.lower()


class TestSecurityHeaders:
    """Tests des headers de sécurité."""
    
    def test_no_server_header_exposure(self, client: TestClient):
        """Test que les headers serveur ne sont pas exposés."""
        response = client.get("/")
        
        # Vérification que certains headers sensibles ne sont pas présents
        sensitive_headers = ["server", "x-powered-by"]
        
        for header in sensitive_headers:
            assert header not in [h.lower() for h in response.headers.keys()]
    
    def test_content_type_headers(self, client: TestClient):
        """Test des headers Content-Type appropriés."""
        # Endpoint JSON
        response = client.get("/")
        assert response.status_code == 200
        assert "application/json" in response.headers.get("content-type", "")
        
        # Endpoint de santé
        health_response = client.get("/health")
        assert health_response.status_code == 200
        assert "application/json" in health_response.headers.get("content-type", "")
