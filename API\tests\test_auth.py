"""
Tests pour l'authentification admin

Tests complets pour valider le système d'authentification JWT,
les endpoints de connexion et la sécurité.
"""

import json
import pytest
from datetime import datetime, timedelta
from fastapi.testclient import TestClient
from jose import jwt

from main import app
from app.config import settings
from app.services.auth import auth_service
from app.core.security import create_access_token


# Client de test
client = TestClient(app)


class TestAuthService:
    """Tests pour le service d'authentification."""

    def test_load_credentials(self):
        """Test du chargement des credentials depuis le fichier JSON."""
        users = auth_service._load_credentials()

        assert len(users) >= 2  # Au moins admin et moderator
        assert "admin" in users
        assert "<EMAIL>" in users

        admin_user = users["admin"]
        assert admin_user.username == "admin"
        assert admin_user.email == "<EMAIL>"
        assert admin_user.is_active is True
        assert admin_user.is_superuser is True
        assert "admin" in admin_user.roles

    def test_verify_password(self):
        """Test de la vérification des mots de passe."""
        # Le mot de passe par défaut est "secret"
        hashed = auth_service.get_password_hash("secret")

        assert auth_service.verify_password("secret", hashed) is True
        assert auth_service.verify_password("wrong", hashed) is False

    def test_authenticate_user_success(self):
        """Test d'authentification réussie."""
        user = auth_service.authenticate_user("admin", "secret")

        assert user is not None
        assert user.username == "admin"
        assert user.is_active is True

    def test_authenticate_user_wrong_password(self):
        """Test d'authentification avec mauvais mot de passe."""
        user = auth_service.authenticate_user("admin", "wrong_password")
        assert user is None

    def test_authenticate_user_nonexistent(self):
        """Test d'authentification avec utilisateur inexistant."""
        user = auth_service.authenticate_user("nonexistent", "secret")
        assert user is None

    def test_get_user(self):
        """Test de récupération d'un utilisateur."""
        user = auth_service.get_user("admin")
        assert user is not None
        assert user.username == "admin"

        # Test avec email
        user_by_email = auth_service.get_user("<EMAIL>")
        assert user_by_email is not None
        assert user_by_email.username == "admin"

    def test_create_access_token(self):
        """Test de création de token JWT."""
        data = {"sub": "admin"}
        token = auth_service.create_access_token(data)

        assert token is not None
        assert isinstance(token, str)

        # Vérifier le contenu du token
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        assert payload["sub"] == "admin"
        assert "exp" in payload

    def test_verify_token(self):
        """Test de vérification de token JWT."""
        data = {"sub": "admin"}
        token = auth_service.create_access_token(data)

        payload = auth_service.verify_token(token)
        assert payload is not None
        assert payload["sub"] == "admin"

        # Test avec token invalide
        invalid_payload = auth_service.verify_token("invalid_token")
        assert invalid_payload is None


class TestAuthEndpoints:
    """Tests pour les endpoints d'authentification."""

    def test_login_success(self):
        """Test de connexion réussie."""
        response = client.post(
            "/api/v1/auth/login",
            json={
                "username": "admin",
                "password": "secret"
            }
        )

        assert response.status_code == 200
        data = response.json()

        assert "access_token" in data
        assert data["token_type"] == "bearer"
        assert "expires_in" in data
        assert "user" in data

        user = data["user"]
        assert user["username"] == "admin"
        assert user["email"] == "<EMAIL>"
        assert user["is_active"] is True
        assert user["is_superuser"] is True

    def test_login_with_email(self):
        """Test de connexion avec email."""
        response = client.post(
            "/api/v1/auth/login",
            json={
                "username": "<EMAIL>",
                "password": "secret"
            }
        )

        assert response.status_code == 200
        data = response.json()
        assert data["user"]["username"] == "admin"

    def test_login_wrong_password(self):
        """Test de connexion avec mauvais mot de passe."""
        response = client.post(
            "/api/v1/auth/login",
            json={
                "username": "admin",
                "password": "wrong_password"
            }
        )

        assert response.status_code == 401
        data = response.json()
        assert "message" in data
        assert "incorrect" in data["message"].lower()

    def test_login_nonexistent_user(self):
        """Test de connexion avec utilisateur inexistant."""
        response = client.post(
            "/api/v1/auth/login",
            json={
                "username": "nonexistent",
                "password": "secret"
            }
        )

        assert response.status_code == 401

    def test_login_invalid_data(self):
        """Test de connexion avec données invalides."""
        response = client.post(
            "/api/v1/auth/login",
            json={
                "username": "",
                "password": ""
            }
        )

        assert response.status_code == 422

    def test_get_current_user_info(self):
        """Test de récupération des informations utilisateur."""
        # D'abord se connecter
        login_response = client.post(
            "/api/v1/auth/login",
            json={
                "username": "admin",
                "password": "secret"
            }
        )

        assert login_response.status_code == 200
        token = login_response.json()["access_token"]

        # Récupérer les infos utilisateur
        response = client.get(
            "/api/v1/auth/me",
            headers={"Authorization": f"Bearer {token}"}
        )

        assert response.status_code == 200
        data = response.json()

        assert data["username"] == "admin"
        assert data["email"] == "<EMAIL>"
        assert data["is_superuser"] is True
        assert "permissions" in data
        assert "admin:all" in data["permissions"]

    def test_get_current_user_info_no_token(self):
        """Test de récupération des infos sans token."""
        response = client.get("/api/v1/auth/me")

        assert response.status_code == 403  # Forbidden par HTTPBearer

    def test_get_current_user_info_invalid_token(self):
        """Test de récupération des infos avec token invalide."""
        response = client.get(
            "/api/v1/auth/me",
            headers={"Authorization": "Bearer invalid_token"}
        )

        assert response.status_code == 401

    def test_logout(self):
        """Test de déconnexion."""
        # D'abord se connecter
        login_response = client.post(
            "/api/v1/auth/login",
            json={
                "username": "admin",
                "password": "secret"
            }
        )

        token = login_response.json()["access_token"]

        # Se déconnecter
        response = client.post(
            "/api/v1/auth/logout",
            headers={"Authorization": f"Bearer {token}"}
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True

    def test_get_all_users_as_superuser(self):
        """Test de récupération de tous les utilisateurs (superuser)."""
        # Se connecter en tant qu'admin (superuser)
        login_response = client.post(
            "/api/v1/auth/login",
            json={
                "username": "admin",
                "password": "secret"
            }
        )

        token = login_response.json()["access_token"]

        # Récupérer tous les utilisateurs
        response = client.get(
            "/api/v1/auth/users",
            headers={"Authorization": f"Bearer {token}"}
        )

        assert response.status_code == 200
        data = response.json()

        assert isinstance(data, list)
        assert len(data) >= 2  # Au moins admin et moderator

        # Vérifier qu'on a bien l'admin
        admin_user = next((u for u in data if u["username"] == "admin"), None)
        assert admin_user is not None
        assert admin_user["is_superuser"] is True

    def test_get_all_users_as_regular_user(self):
        """Test de récupération de tous les utilisateurs (utilisateur normal)."""
        # Se connecter en tant que moderator (pas superuser)
        login_response = client.post(
            "/api/v1/auth/login",
            json={
                "username": "moderator",
                "password": "secret"
            }
        )

        token = login_response.json()["access_token"]

        # Essayer de récupérer tous les utilisateurs
        response = client.get(
            "/api/v1/auth/users",
            headers={"Authorization": f"Bearer {token}"}
        )

        assert response.status_code == 403  # Forbidden


class TestJWTSecurity:
    """Tests pour la sécurité JWT."""

    def test_token_expiration(self):
        """Test d'expiration de token."""
        # Créer un token expiré
        data = {"sub": "admin"}
        expired_token = create_access_token(
            data=data,
            expires_delta=timedelta(seconds=-1)  # Expiré il y a 1 seconde
        )

        # Essayer d'utiliser le token expiré
        response = client.get(
            "/api/v1/auth/me",
            headers={"Authorization": f"Bearer {expired_token}"}
        )

        assert response.status_code == 401

    def test_token_with_invalid_user(self):
        """Test avec token contenant un utilisateur inexistant."""
        data = {"sub": "nonexistent_user"}
        token = create_access_token(data=data)

        response = client.get(
            "/api/v1/auth/me",
            headers={"Authorization": f"Bearer {token}"}
        )

        assert response.status_code == 401
